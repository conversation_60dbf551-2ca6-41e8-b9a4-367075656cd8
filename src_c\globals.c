#include "globals.h"

// Instruction handling
volatile InstructionCode instruction_byte  = NONE;
volatile uint8_t instruction_data_ls_byte  = 0x00;
volatile uint8_t instruction_data_ms_byte  = 0x00;

// UART transmission
volatile uint8_t uart_tx_byte_pending  = 0xFF;
volatile bool is_uart_tx_byte_pending  = false;

// UART reception
unsigned char received_buffer[8];
volatile bool is_UART_receiving = false;

// ADC
volatile uint8_t adc_ls_byte   = 0;
volatile uint8_t adc_ms_byte   = 0;
volatile bool is_adc_complete  = false;
volatile bool is_adc_running   = false;

// System state
volatile SystemState state = AWAIT;

// Input change tracking
volatile bool has_switch_input_changed  = false;
volatile bool has_ADC_input_changed     = false;
