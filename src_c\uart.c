#define F_CPU 16000000UL
#include "uart.h"
#include <avr/io.h>
#include <avr/interrupt.h>
#include <stdbool.h>

#include "globals.h"


/*=== Configuration ===*/
void uart_setup() {
  set_UBRR_using_baud_rate(38400);  // Set initial baud rate
  UCSR1B =
    (1 << RXCIE1) |  // Enable RX Complete Interrupt
    (1 << TXCIE1) |  // Enable TX Complete Interrupt
    (1 << UDRIE1) |  // Enable Data Register Empty Interrupt
    (1 << RXEN1) |   // Enable Receiver
    (1 << TXEN1);    // Enable Transmitter
  UCSR1C =
    (1 << UCSZ11) |  // Set 8-bit data frame
    (1 << UCSZ10);   // 1 stop bit with no parity
}

static void set_UBRR_using_baud_rate(uint16_t baud_rate) {
  uint16_t UBRR_value = (F_CPU / (16 * baud_rate)) - 1;
  UBRR1H = (uint8_t)(UBRR_value >> 8);
  UBRR1L = (uint8_t)(UBRR_value);
}

// Queue byte for transmission via USART1
void transmit_byte(uint8_t new_tx_byte) {
  cli();                               // Disabling interrupts ensures following is not interupted and breaks
  uart_tx_byte_pending = new_tx_byte;  // Replace old uart_tx_byte_pending with new
  is_uart_tx_byte_pending = true;
  sei();                               // Re-enables interrupts
  UCSR1B |= (1<<UDRIE1);               // Enable UDRE interrupt
}


/*=== ISR for TX complete ===*/
// Transmit pending byte once UDR is clear
ISR(USART1_UDRE_vect) {
  if (is_uart_tx_byte_pending) {
    UDR1 = uart_tx_byte_pending;       // Send byte
    is_uart_tx_byte_pending = false;
    UCSR1B &= ~(1<<UDRIE1);           // Disable UDRE interrupt
  }
}


/*=== ISR for RX complete ===*/
static volatile enum {
  IDLE,
  WAITING_INSTRUCTION,
  WAITING_LS,
  WAITING_MS,
  WAITING_STOP
} uart_rx_state = IDLE;  // States of the RX protocol

static volatile bool start_byte_received = false;

// Reset the protocol state machine
void uart_protocol_reset(void) { 
  uart_rx_state             = IDLE;
  start_byte_received       = false;
  instruction_byte          = NONE;  // Invalid instruction until we receive one
  instruction_data_ls_byte  = 0x00;
  instruction_data_ms_byte  = 0x00;
}

ISR(USART1_RX_vect) {
  uint8_t received_byte = UDR1;              // Get the received byte from the USART data register
  
  switch (received_byte) {
    case 0x53:                               // Start byte received
      uart_protocol_reset();                 // Reset the protocol state machine
      start_byte_received = true;
      uart_rx_state = WAITING_INSTRUCTION;
      break;
      
    case 0xAA:                               // Stop byte received
      if (uart_rx_state == WAITING_STOP) {   // Complete valid packet received, process instructions
        uart_rx_state = IDLE;
        start_byte_received = false;
      } else {                               // Unexpected stop byte, reset state machine
        uart_protocol_reset(); 
      }
      break;
      
    default:
      if (!start_byte_received) return;

      switch (uart_rx_state) {
        case WAITING_INSTRUCTION:            // Wait for instruction byte
          instruction_byte = received_byte;  // Store instruction byte and determine next expected bytes
          if (received_byte <= 0x05) {       // Simple instructions (0x00-0x05) only need stop byte next
            uart_rx_state = WAITING_STOP;
            state = PROCESS_SHORT_INSTRUCTION;
          } else if (0x0A <= received_byte && received_byte <= 0x0D) { // Complex instructions (0x0A-0x0D) need both LS and MS bytes
            uart_rx_state = WAITING_LS;
          } else {                           // Invalid instruction value received, reset state machine
            uart_protocol_reset();
          }
          break;

        case WAITING_LS:                     // Wait for and store LS byte and then prepare for MS byte
          instruction_data_ls_byte = received_byte;
          uart_rx_state = WAITING_MS;
          break;

        case WAITING_MS:                     // Wait for and store MS byte and then prepare for stop byte
          instruction_data_ms_byte = received_byte;
          uart_rx_state = WAITING_STOP;
          state = PROCESS_LONG_INSTRUCTION;
          break;

        default:                             // Unexpected byte received, reset state machine
          uart_protocol_reset();
          state = AWAIT;
          break;
      }
  }
}