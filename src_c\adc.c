#include "adc.h"
#include <avr/io.h>
#include <avr/interrupt.h>
#include <stdbool.h>

#include "globals.h"

void adc_setup(){
  ADMUX  =
    (1 << REFS0);  // AVcc reference
  ADCSRA =
    (1 << ADEN) |                 // Enable ADC
    (1 << ADIE) |                 // Enable ADC Interrupt
    (1 << ADPS2) | (1 << ADPS0);  // Set prescaler to 32
}

void get_ADC_val(uint8_t adc_channel_index) {
  ADMUX = 
    (ADMUX & 0b11111100) |             // Mask so that only the bits to select from ADC0-ADC2 are changeable
    (adc_channel_index & 0b00000011);  // Set the channel index
  ADCSRA |= (1 << ADSC);               // Start conversion

  is_adc_running   = true;
  is_adc_complete  = false;
}

/*=== ISR for ADC conversion complete ===*/
ISR(ADC_vect) {
  adc_ls_byte  = ADCL;
  adc_ms_byte  = ADCH;

  is_adc_running   = false;
  is_adc_complete  = true;
}
