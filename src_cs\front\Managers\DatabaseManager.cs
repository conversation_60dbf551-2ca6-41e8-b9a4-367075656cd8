﻿using MySql.Data.MySqlClient;
using System;

namespace front.Managers
{
    public class DatabaseManager
    {
        private static readonly Lazy<DatabaseManager> _instance = new(() => new DatabaseManager());
        public static DatabaseManager Instance => _instance.Value;

        private MySqlConnection _connection;
        public bool IsConnected => _connection?.State == System.Data.ConnectionState.Open;

        private DatabaseManager() { }

        public bool Connect(string server, string username, string password, string database, out string error)
        {
            error = string.Empty;

            string connStr = $"server={server};uid={username};pwd={password};database={database};";
            _connection = new MySqlConnection(connStr);

            try
            {
                _connection.Open();
                return true;
            }
            catch (Exception ex)
            {
                error = ex.Message;
                _connection = null;
                return false;
            }
        }

        public void Disconnect()
        {
            if (IsConnected)
            {
                _connection.Close();
                _connection.Dispose();
                _connection = null;
            }
        }

        public bool InsertTemperatureRecord(string timestamp, string temp, string remark, out string error)
        {
            error = string.Empty;

            if (!IsConnected)
            {
                error = "Not connected to database.";
                return false;
            }

            string sql = "INSERT INTO temperature (timeStamp, temperature, remark) VALUES (@ts, @temp, @rem)";

            try
            {
                using var cmd = new MySqlCommand(sql, _connection);
                cmd.Parameters.AddWithValue("@ts", timestamp);
                cmd.Parameters.AddWithValue("@temp", temp);
                cmd.Parameters.AddWithValue("@rem", remark);
                cmd.ExecuteNonQuery();
                return true;
            }
            catch (Exception ex)
            {
                error = ex.Message;
                return false;
            }
        }
    }
}
