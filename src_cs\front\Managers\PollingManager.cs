using System;
using System.Windows.Forms;

namespace front.Managers
{
    public class PollingManager
    {
        private readonly System.Windows.Forms.Timer _pollTimer;

        // Event for Port A updates
        public event Action<byte>? OnPortAUpdated;

        public PollingManager()
        {
            _pollTimer = new System.Windows.Forms.Timer
            {
                Interval = 500 // in milliseconds
            };
            _pollTimer.Tick += PollTimer_Tick;
        }

        private void PollTimer_Tick(object? sender, EventArgs e)
        {
            try
            {
                if (SerialManager.Instance.IsConnected)
                {
                    byte pinState = SerialManager.Instance.ReadPINA();
                    OnPortAUpdated?.Invoke(pinState);
                }
            }
            catch (Exception ex)
            {
                // Optional: log or show error
                Console.WriteLine($"Polling error: {ex.Message}");
            }
        }

        public void StartPolling() => _pollTimer.Start();

        public void StopPolling() => _pollTimer.Stop();
    }
}
