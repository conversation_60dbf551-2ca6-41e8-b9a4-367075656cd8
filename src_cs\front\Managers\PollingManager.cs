using System;
using System.Windows.Forms;

namespace front.Managers
{
    public class PollingManager
    {
        private readonly Timer _pollTimer;
        private readonly Action<byte> _updateCallback;

        public PollingManager(Action<byte> updateCallback)
        {
            _updateCallback = updateCallback;
            _pollTimer = new Timer
            {
                Interval = 500 // in milliseconds
            };
            _pollTimer.Tick += PollTimer_Tick;
        }

        private void PollTimer_Tick(object sender, EventArgs e)
        {
            try
            {
                if (SerialManager.Instance.IsConnected)
                {
                    byte pinState = SerialManager.Instance.ReadPINA();
                    _updateCallback?.Invoke(pinState);
                }
            }
            catch (Exception ex)
            {
                // Optional: log or show error
                Console.WriteLine($"Polling error: {ex.Message}");
            }
        }

        public void Start() => _pollTimer.Start();

        public void Stop() => _pollTimer.Stop();
    }
}
