using System;
using System.IO.Ports;
using System.Linq;
using System.Threading;
using System.Windows.Forms;
using front.Managers;

namespace front
{
    public partial class MainForm : Form
    {
        private readonly AppBoard _appBoard = AppBoard.Instance;

        // Single timer as per specification
        private System.Windows.Forms.Timer appTimer;
        private EventHandler ev; // Store current event handler

        private bool isDbConnected = false;

        public MainForm()
        {
            InitializeComponent();
            InitializeApp();
        }

        private void InitializeApp()
        {
            // Setup Tab
            comPort_text.Items.AddRange(_appBoard.getCOMPorts());
            if (comPort_text.Items.Count > 0)
                comPort_text.SelectedIndex = 0;

            baudrate_text.Items.AddRange(AppBoard.GetBaudRates().Cast<object>().ToArray());
            baudrate_text.SelectedItem = 38400;

            // Database Tab
            serverName_drop.Items.AddRange(new string[] { "localhost", "127.0.0.1" });
            if (serverName_drop.Items.Count > 0)
                serverName_drop.SelectedIndex = 0;

            serialPortStatus_bulb.On = false;
            dbServerConnStatus_bulb.On = false;

            serialPortConnection_connect_button.Enabled = true;
            serialPortConnection_disconnect_button.Enabled = false;
            serialPortConnection_connect_button.Click += ConnectClick;
            serialPortConnection_disconnect_button.Click += DisconnectClick;

            dbServerConnection_connect_button.Enabled = true;
            dbServerConnection_disconnect_button.Enabled = false;
            dbServerConnection_connect_button.Click += dbServerConnection_connect_button_Click;
            dbServerConnection_disconnect_button.Click += dbServerConnection_disconnect_button_Click;

            dbServerConnStatus_bulb.On = false;
            dbServerStatus_label.Text = "Disconnected";

            // Digital I/O Tab
            sevenSegment1.Value = "0";
            sevenSegment2.Value = "0";

            pc0.CheckedChanged += portC_CheckboxChanged;
            pc1.CheckedChanged += portC_CheckboxChanged;
            pc2.CheckedChanged += portC_CheckboxChanged;
            pc3.CheckedChanged += portC_CheckboxChanged;
            pc4.CheckedChanged += portC_CheckboxChanged;
            pc5.CheckedChanged += portC_CheckboxChanged;
            pc6.CheckedChanged += portC_CheckboxChanged;
            pc7.CheckedChanged += portC_CheckboxChanged;

            pa0_bulb.On = false;
            pa1_bulb.On = false;
            pa2_bulb.On = false;
            pa3_bulb.On = false;
            pa4_bulb.On = false;
            pa5_bulb.On = false;
            pa6_bulb.On = false;
            pa7_bulb.On = false;

            // Initialize timers
            InitializeTimers();

            // Initialize UI components
            InitializePots();
            InitializeLight();

            // Subscribe to tab change events
            tabs.SelectedIndexChanged += appTabs_SelectedIndexChanged;
        }

        #region Required Event Handlers

        // ConnectClick [Event Handler that is run when the Connect Button is clicked]
        private void ConnectClick(object sender, EventArgs e)
        {
            string port = comPort_text.SelectedItem?.ToString();
            if (port == null)
            {
                MessageBox.Show("Select a COM port first.");
                return;
            }

            int baud = (int)baudrate_text.SelectedItem;

            _appBoard.setCOMPort(port);
            _appBoard.setBaudrate(baud);

            if (_appBoard.Connect())
            {
                UpdateSerialConnectionUI();
                // Test communication
                try
                {
                    byte result = _appBoard.checkTx();
                    if (result == 0x0F)
                    {
                        // Communication successful, trigger tab-based timer
                        appTabs_SelectedIndexChanged(tabs, EventArgs.Empty);
                    }
                    else
                    {
                        MessageBox.Show("Communication test failed. Check MCU connection.");
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Communication test failed: {ex.Message}");
                }
            }
        }

        private void DisconnectClick(object sender, EventArgs e)
        {
            appTimer?.Stop();
            _appBoard.Disconnect();
            UpdateSerialConnectionUI();
        }

        // RefreshClick [Event Handler that is run when the Refresh Button is clicked (optional)]
        private void RefreshClick(object sender, EventArgs e)
        {
            // Optional refresh functionality
            if (_appBoard.IsConnected)
            {
                // Force update all readings
                UpdateTimerEvent(sender, e);
            }
        }

        // appTabs_SelectedIndexChanged [Event Handler that is run when a Tab is selected]
        private void appTabs_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (!_appBoard.IsConnected) return;

            switch (tabs.SelectedIndex)
            {
                case 0: // Setup Tab
                    appTimer.Stop();
                    break;
                case 1: // Digital I/O Tab
                    appTimer.Interval = 500;
                    UpdateTimerEvent(digIO_Tick);
                    break;
                case 2: // Pots-Light Tab
                    appTimer.Interval = 200;
                    UpdateTimerEvent(Pot_Tick);
                    break;
                case 3: // Temperature Control Tab
                    appTimer.Interval = 50; // 20Hz for PI controller
                    UpdateTimerEvent(Temp_Tick);
                    break;
                default:
                    appTimer.Stop();
                    break;
            }
        }

        #endregion

        #region Timer Methods

        private void InitializeTimers()
        {
            // Single timer as per specification
            appTimer = new System.Windows.Forms.Timer { Interval = 100 };
        }

        // UpdateTimerEvent [As per the C# timer section]
        // Removes existing tick events and adds the new one
        private void UpdateTimerEvent(EventHandler newev)
        {
            appTimer.Stop(); // Stop the Timer
            if (ev != null) // If we have a previous event
                appTimer.Tick -= ev; // Remove the previous tick event
            appTimer.Tick += newev; // Add our new one
            ev = newev; // Save the new one
            appTimer.Start(); // Restart the timer
        }

        // digIO_Tick [Tick Event to control Digital I/O Functionality]
        private void digIO_Tick(object sender, EventArgs e)
        {
            if (!_appBoard.IsConnected) return;

            try
            {
                byte pinState = _appBoard.readPINA();
                UpdatePortALeds(pinState);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Digital I/O error: {ex.Message}");
            }
        }

        // Pot_Tick [Tick Event to control Potentiometer Functionality]
        // Also handles Lamp functionality since they're on the same tab
        private void Pot_Tick(object sender, EventArgs e)
        {
            if (!_appBoard.IsConnected) return;

            try
            {
                // Read potentiometers
                byte pot1Value = _appBoard.readPotV(1);
                byte pot2Value = _appBoard.readPotV(2);

                float pot1Voltage = ConvertAdcToVoltage(pot1Value);
                float pot2Voltage = ConvertAdcToVoltage(pot2Value);

                pot1.Value = pot1Voltage;
                pot2.Value = pot2Voltage;

                pot1_label.Text = $"Pot 1: {pot1Voltage:F2}V (ADC: {pot1Value})";
                pot2_label.Text = $"Pot 2: {pot2Voltage:F2}V (ADC: {pot2Value})";

                // Read light sensor for lamp functionality
                byte lightValue = _appBoard.readLight();
                float lightVoltage = ConvertAdcToVoltage(lightValue);
                lightPot.Value = lightVoltage;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Potentiometer/Lamp error: {ex.Message}");
                pot1_label.Text = "Pot 1: Error";
                pot2_label.Text = "Pot 2: Error";
            }
        }

        // Lamp_Tick [Tick Event to control Lamp Functionality]
        // Note: Lamp functionality is now handled in Pot_Tick since they're on the same tab
        private void Lamp_Tick(object sender, EventArgs e)
        {
            // This method is kept for compatibility but functionality moved to Pot_Tick
            Pot_Tick(sender, e);
        }

        // PI Controller variables
        private double integralError = 0;
        private double setpointTemp = 25.0; // Default setpoint
        private double Kp = 1.0; // Proportional gain
        private double Ki = 0.1; // Integral gain
        private double dt = 0.05; // 20Hz sampling (50ms)

        // Temp_Tick [Tick Event to control Temperature Control Functionality]
        private void Temp_Tick(object sender, EventArgs e)
        {
            if (!_appBoard.IsConnected) return;

            try
            {
                // 1. Set heater to 100%
                _appBoard.writeHeater(399); // Max PWM value

                // 2. Read current temperature
                byte tempValue = _appBoard.readTemp();
                float tempVoltage = ConvertAdcToVoltage(tempValue);
                double currentTemp = tempVoltage * 100; // Convert to °C (50mV/°C sensor)

                // 3. Calculate error (measured - setpoint as per spec)
                double error = currentTemp - setpointTemp;

                // 4. Calculate integral error
                integralError += error * dt;

                // 5. Calculate PI controller output
                double output = Kp * error + Ki * integralError;

                // 6. Saturate output to 0-100%
                output = Math.Max(0, Math.Min(100, output));

                // 7. Convert to motor PWM value (0-399)
                ushort motorPWM = (ushort)(output * 399 / 100);

                // 8. Write to motor
                _appBoard.writeMotor(motorPWM);

                // Update UI if temperature controls exist
                Console.WriteLine($"Temp: {currentTemp:F1}°C, Setpoint: {setpointTemp:F1}°C, Motor: {output:F1}%");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Temperature control error: {ex.Message}");
            }
        }

        #endregion

        private void UpdatePortALeds(byte portA)
        {
            pa0_bulb.On = (portA & 0b00000001) != 0;
            pa1_bulb.On = (portA & 0b00000010) != 0;
            pa2_bulb.On = (portA & 0b00000100) != 0;
            pa3_bulb.On = (portA & 0b00001000) != 0;
            pa4_bulb.On = (portA & 0b00010000) != 0;
            pa5_bulb.On = (portA & 0b00100000) != 0;
            pa6_bulb.On = (portA & 0b01000000) != 0;
            pa7_bulb.On = (portA & 0b10000000) != 0;
        }

        private void UpdateSerialConnectionUI()
        {
            bool isConnected = _appBoard.IsConnected;

            serialPortStatus_bulb.On = isConnected;
            serialPortStatus_label.Text = isConnected
              ? $"Connected to {_appBoard.GetConnectedPortName()}"
              : "Disconnected";

            serialPortConnection_connect_button.Enabled = !isConnected;
            serialPortConnection_disconnect_button.Enabled = isConnected;

            comPort_text.Enabled = !isConnected;
            baudrate_text.Enabled = !isConnected;
        }



        private void dbServerConnection_connect_button_Click(object sender, EventArgs e)
        {
            if (!isDbConnected)
            {
                string server = serverName_drop.SelectedItem?.ToString()?.Trim();
                string username = username_text.Text.Trim();
                string password = password_text.Text;
                string dbName = db_text.Text.Trim();

                if (string.IsNullOrEmpty(server) || string.IsNullOrEmpty(username) || string.IsNullOrEmpty(dbName))
                {
                    MessageBox.Show("Please fill in all required database fields.", "Missing Information", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (DatabaseManager.Instance.Connect(server, username, password, dbName, out string error))
                {
                    isDbConnected = true;
                    dbServerConnStatus_bulb.On = true;
                    dbServerStatus_label.Text = "Connected to DB";
                    dbServerConnection_connect_button.Enabled = false;
                    dbServerConnection_disconnect_button.Enabled = true;

                    serverName_drop.Enabled = false;
                    username_text.Enabled = false;
                    password_text.Enabled = false;
                    db_text.Enabled = false;
                }
                else
                {
                    MessageBox.Show($"Failed to connect to database:\n{error}", "Database Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void dbServerConnection_disconnect_button_Click(object sender, EventArgs e)
        {
            if (isDbConnected)
            {
                DatabaseManager.Instance.Disconnect();
                isDbConnected = false;
                dbServerConnStatus_bulb.On = false;
                dbServerStatus_label.Text = "Disconnected";
                dbServerConnection_connect_button.Enabled = true;
                dbServerConnection_disconnect_button.Enabled = false;

                serverName_drop.Enabled = true;
                username_text.Enabled = true;
                password_text.Enabled = true;
                db_text.Enabled = true;
            }
        }

        private void portC_CheckboxChanged(object sender, EventArgs e)
        {
            byte portValue = 0;
            if (pc0.Checked) portValue |= 0b00000001;
            if (pc1.Checked) portValue |= 0b00000010;
            if (pc2.Checked) portValue |= 0b00000100;
            if (pc3.Checked) portValue |= 0b00001000;
            if (pc4.Checked) portValue |= 0b00010000;
            if (pc5.Checked) portValue |= 0b00100000;
            if (pc6.Checked) portValue |= 0b01000000;
            if (pc7.Checked) portValue |= 0b10000000;

            sevenSegment1.Value = portValue.ToString("X2")[0].ToString();
            sevenSegment2.Value = portValue.ToString("X2")[1].ToString();

            if (_appBoard.IsConnected)
            {
                try
                {
                    _appBoard.writePORTC(portValue);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error writing to PORTC: {ex.Message}");
                }
            }
        }

        // FormClosing [Event Handler that is run when the form is closing, to reset all MCU peripherals]
        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            base.OnFormClosing(e);

            // Reset all MCU peripherals
            if (_appBoard.IsConnected)
            {
                try
                {
                    _appBoard.writePORTC(0);     // Reset PORTC
                    _appBoard.writeLight(0);     // Turn off light
                    _appBoard.writeHeater(0);    // Turn off heater
                    _appBoard.writeMotor(0);     // Turn off motor
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error resetting MCU peripherals: {ex.Message}");
                }
            }

            appTimer?.Stop();
            _appBoard.Disconnect();
        }


        private void InitializePots()
        {
            // Configure pot gauges
            ConfigurePotGauge(pot1, "Pot 1");
            ConfigurePotGauge(pot2, "Pot 2");
        }

        private static void ConfigurePotGauge(AquaControls.AquaGauge gauge, string dialText)
        {
            gauge.MinValue = 0f;
            gauge.MaxValue = 5f; // 5V max
            gauge.DialText = dialText;
            gauge.Value = 0f;

            // Set gauge appearance
            gauge.RecommendedValue = 2.5f; // Middle value
            gauge.ThresholdPercent = 80f; // Warning at 80%
        }

        private static float ConvertAdcToVoltage(byte adcValue)
        {
            // Convert 8-bit ADC value (0-255) to voltage (0-5V)
            return adcValue / 255f * 5f;
        }

        private void InitializeLight()
        {
            // Configure light gauge
            lightPot.MinValue = 0f;
            lightPot.MaxValue = 5f;
            lightPot.DialText = "Light Level";
            lightPot.Value = 0f;
            lightPot.ThresholdPercent = 80f;

            // Configure scroll bar
            lightPot_scroll.Minimum = 0;
            lightPot_scroll.Maximum = 264; // 255 + width of bar 9
            lightPot_scroll.Value = 0;
            lightPot_scroll.ValueChanged += LightPot_scroll_ValueChanged;

            // Configure text box to show scroll bar value
            lightPot_text.Text = "0";
            lightPot_text.ReadOnly = true;
        }



        private void LightPot_scroll_ValueChanged(object? sender, EventArgs e)
        {
            try
            {
                int scrollValue = lightPot_scroll.Value;
                lightPot_text.Text = scrollValue.ToString();

                // Send the scroll bar value to MCU to adjust lamp brightness
                if (_appBoard.IsConnected)
                {
                    _appBoard.writeLight((ushort)scrollValue);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error setting light: {ex.Message}", "Light Control Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }


    }
}
