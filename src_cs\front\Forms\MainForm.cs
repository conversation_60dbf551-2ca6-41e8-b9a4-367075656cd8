using System;
using System.IO.Ports;
using System.Linq;
using System.Threading;
using System.Windows.Forms;
using front.Managers;

namespace front
{
    public partial class MainForm : Form
    {
        private readonly SerialManager _serialManager = SerialManager.Instance;
        private readonly DatabaseManager _dbManager;
        private System.Windows.Forms.Timer pinaPollTimer;
        private CancellationTokenSource _cts;
        private PollingManager _pollingManager;
        private System.Windows.Forms.Timer _potsTimer;


        private bool isSerialConnected = false;
        private bool isDbConnected = false;
        private SerialPort serialPort = new SerialPort();

        public MainForm()
        {
            InitializeComponent();
            InitializeApp();
        }

        private void InitializeApp()
        {
            // Setup Tab
            comPort_text.Items.AddRange(SerialPort.GetPortNames());
            if (comPort_text.Items.Count > 0)
                comPort_text.SelectedIndex = 0;

            baudrate_text.Items.AddRange(new object[] { 9600, 19200, 38400, 57600, 115200 });
            baudrate_text.SelectedItem = 38400;

            serialPortStatus_bulb.On = false;
            dbServerConnStatus_bulb.On = false;

            serialPortConnection_connect_button.Enabled = true;
            serialPortConnection_disconnect_button.Enabled = false;
            serialPortConnection_connect_button.Click += serialPortConnection_connect_button_Click;
            serialPortConnection_disconnect_button.Click += serialPortConnection_disconnect_button_Click;

            dbServerConnection_connect_button.Enabled = true;
            dbServerConnection_disconnect_button.Enabled = false;

            dbServerConnStatus_bulb.On = false;
            dbServerStatus_label.Text = "Disconnected";

            // Digital I/O Tab
            sevenSegment1.Value = "0";
            sevenSegment2.Value = "0";

            pc0.CheckedChanged += portC_CheckboxChanged;
            pc1.CheckedChanged += portC_CheckboxChanged;
            pc2.CheckedChanged += portC_CheckboxChanged; 
            pc3.CheckedChanged += portC_CheckboxChanged;
            pc4.CheckedChanged += portC_CheckboxChanged;
            pc5.CheckedChanged += portC_CheckboxChanged;
            pc6.CheckedChanged += portC_CheckboxChanged;
            pc7.CheckedChanged += portC_CheckboxChanged;

            pa0_bulb.On = false;
            pa1_bulb.On = false;
            pa2_bulb.On = false;
            pa3_bulb.On = false;
            pa4_bulb.On = false;
            pa5_bulb.On = false;
            pa6_bulb.On = false;
            pa7_bulb.On = false;

            _pollingManager = new PollingManager();
            _pollingManager.OnPortAUpdated += UpdatePortALeds;
            _pollingManager.OnLightUpdated += UpdateLightGauge;
            _pollingManager.StartPolling();

            // Pots Tab
            InitializePots();
            InitializeLight();

        }

        private void UpdatePortALeds(byte portA)
        {
            // Assume you have pa0_bulb ... pa7_bulb
            pa0_bulb.On = (portA & 0b00000001) != 0;
            pa1_bulb.On = (portA & 0b00000010) != 0;
            pa2_bulb.On = (portA & 0b00000100) != 0;
            pa3_bulb.On = (portA & 0b00001000) != 0;
            pa4_bulb.On = (portA & 0b00010000) != 0;
            pa5_bulb.On = (portA & 0b00100000) != 0;
            pa6_bulb.On = (portA & 0b01000000) != 0;
            pa7_bulb.On = (portA & 0b10000000) != 0;
        }

        private void UpdateLightGauge(byte lightValue)
        {
            float lightVoltage = ConvertAdcToVoltage(lightValue);
            lightPot.Value = lightVoltage;
        }

        private void UpdateSerialConnectionUI()
        {
            bool isConnected = SerialManager.Instance.IsConnected;

            serialPortStatus_bulb.On = isConnected;
            serialPortStatus_label.Text = isConnected
              ? $"Connected to {SerialManager.Instance.GetConnectedPortName()}"
              : "Disconnected";

            serialPortConnection_connect_button.Enabled = !isConnected;
            serialPortConnection_disconnect_button.Enabled = isConnected;

            comPort_text.Enabled = !isConnected;
            baudrate_text.Enabled = !isConnected;
        }

        private void serialPortConnection_connect_button_Click(object sender, EventArgs e)
        {
            if (!SerialManager.Instance.IsConnected)
            {
                string port = comPort_text.SelectedItem?.ToString();
                if (port == null)
                {
                    MessageBox.Show("Select a COM port first.");
                    return;
                }

                int baud = (int)baudrate_text.SelectedItem;

                SerialManager.Instance.Connect(port, baud);
                UpdateSerialConnectionUI();
            }
        }

        private void serialPortConnection_disconnect_button_Click(object sender, EventArgs e)
        {
            if (SerialManager.Instance.IsConnected)
            {
                SerialManager.Instance.Disconnect();
                UpdateSerialConnectionUI();
            }
        }

        private void dbServerConnection_connect_button_Click(object sender, EventArgs e)
        {
            if (!isDbConnected)
            {
                string server = serverName_drop.SelectedItem?.ToString()?.Trim();
                string username = username_text.Text.Trim();
                string password = password_text.Text;
                string dbName = db_text.Text.Trim();

                if (string.IsNullOrEmpty(server) || string.IsNullOrEmpty(username) || string.IsNullOrEmpty(dbName))
                {
                    MessageBox.Show("Please fill in all required database fields.", "Missing Information", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (DatabaseManager.Instance.Connect(server, username, password, dbName, out string error))
                {
                    isDbConnected = true;
                    dbServerConnStatus_bulb.On = true;
                    dbServerStatus_label.Text = "Connected to DB";
                    dbServerConnection_connect_button.Enabled = false;
                    dbServerConnection_disconnect_button.Enabled = true;

                    serverName_drop.Enabled = false;
                    username_text.Enabled = false;
                    password_text.Enabled = false;
                    db_text.Enabled = false;
                }
                else
                {
                    MessageBox.Show($"Failed to connect to database:\n{error}", "Database Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void dbServerConnection_disconnect_button_Click(object sender, EventArgs e)
        {
            if (isDbConnected)
            {
                DatabaseManager.Instance.Disconnect();
                isDbConnected = false;
                dbServerConnStatus_bulb.On = false;
                dbServerStatus_label.Text = "Disconnected";
                dbServerConnection_connect_button.Enabled = true;
                dbServerConnection_disconnect_button.Enabled = false;

                serverName_drop.Enabled = true;
                username_text.Enabled = true;
                password_text.Enabled = true;
                db_text.Enabled = true;
            }
        }

        private void portC_CheckboxChanged(object sender, EventArgs e)
        {
            byte portValue = 0;
            if (pc0.Checked) portValue |= 0b00000001;
            if (pc1.Checked) portValue |= 0b00000010;
            if (pc2.Checked) portValue |= 0b00000100;
            if (pc3.Checked) portValue |= 0b00001000;
            if (pc4.Checked) portValue |= 0b00010000;
            if (pc5.Checked) portValue |= 0b00100000;
            if (pc6.Checked) portValue |= 0b01000000;
            if (pc7.Checked) portValue |= 0b10000000;

            sevenSegment1.Value = (portValue.ToString("X2"))[0].ToString();
            sevenSegment2.Value = (portValue.ToString("X2"))[1].ToString();

            if (isSerialConnected)
            {
                serialPort.Write(new byte[] { portValue }, 0, 1);
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            base.OnFormClosing(e);

            if (isSerialConnected) serialPort.Close();
            _pollingManager?.StopPolling();
            _potsTimer?.Stop();

        }


        private void InitializePots()
        {
            // Configure pot gauges
            ConfigurePotGauge(pot1, "Pot 1");
            ConfigurePotGauge(pot2, "Pot 2");

            // Setup timer for automatic pot reading
            _potsTimer = new System.Windows.Forms.Timer
            {
                Interval = 200 // Update every 200 ms
            };
            _potsTimer.Tick += PotsTimer_Tick;
            _potsTimer.Start();
        }

        private void ConfigurePotGauge(AquaControls.AquaGauge gauge, string dialText)
        {
            gauge.MinValue = 0f;
            gauge.MaxValue = 5f; // 5V max
            gauge.DialText = dialText;
            gauge.Value = 0f;

            // Set gauge appearance
            gauge.RecommendedValue = 2.5f; // Middle value
            gauge.ThresholdPercent = 80f; // Warning at 80%
        }

        private void PotsTimer_Tick(object? sender, EventArgs e)
        {
            if (!SerialManager.Instance.IsConnected)
                return;
            try
            {
                // Read Pot 1
                byte pot1Value = SerialManager.Instance.ReadPot1();
                float pot1Voltage = ConvertAdcToVoltage(pot1Value);
                pot1.Value = pot1Voltage;

                // Read Pot 2
                byte pot2Value = SerialManager.Instance.ReadPot2();
                float pot2Voltage = ConvertAdcToVoltage(pot2Value);
                pot2.Value = pot2Voltage;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error reading pots: {ex.Message}");
            }
        }

        private float ConvertAdcToVoltage(byte adcValue)
        {
            // Convert 8-bit ADC value (0-255) to voltage (0-5V)
            return (adcValue / 255f) * 5f;
        }

        private void InitializeLight()
        {
            // Configure light gauge
            lightPot.MinValue = 0f;
            lightPot.MaxValue = 5f;
            lightPot.DialText = "Light Level";
            lightPot.Value = 0f;
            lightPot.ThresholdPercent = 80f;

            // Configure scroll bar
            lightPot_scroll.Minimum = 0;
            lightPot_scroll.Maximum = 264;
            lightPot_scroll.Value = 0;
            lightPot_scroll.ValueChanged += LightPot_scroll_ValueChanged;

            // Configure text box to show scroll bar value
            lightPot_text.Text = "0";
            lightPot_text.ReadOnly = true;
        }



        private void LightPot_scroll_ValueChanged(object? sender, EventArgs e)
        {
            try
            {
                int scrollValue = lightPot_scroll.Value;
                lightPot_text.Text = scrollValue.ToString();

                // Send the scroll bar value to MCU to adjust lamp brightness
                if (SerialManager.Instance.IsConnected)
                {
                    SerialManager.Instance.SetLight((ushort)scrollValue);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error setting light: {ex.Message}", "Light Control Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }


    }
}
