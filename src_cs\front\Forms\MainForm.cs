using System;
using System.IO.Ports;
using System.Linq;
using System.Threading;
using System.Windows.Forms;
using front.Managers;

namespace front
{
    public partial class MainForm : Form
    {
        private readonly AppBoard _appBoard = AppBoard.Instance;
        private readonly DatabaseManager _dbManager;

        // Required timers
        private System.Windows.Forms.Timer _updateTimer;
        private System.Windows.Forms.Timer _digIOTimer;
        private System.Windows.Forms.Timer _potTimer;
        private System.Windows.Forms.Timer _lampTimer;
        private System.Windows.Forms.Timer _tempTimer;

        private bool isDbConnected = false;

        public MainForm()
        {
            InitializeComponent();
            InitializeApp();
        }

        private void InitializeApp()
        {
            // Setup Tab
            comPort_text.Items.AddRange(_appBoard.getCOMPorts());
            if (comPort_text.Items.Count > 0)
                comPort_text.SelectedIndex = 0;

            baudrate_text.Items.AddRange(AppBoard.GetBaudRates().Cast<object>().ToArray());
            baudrate_text.SelectedItem = 38400;

            // Database Tab
            serverName_drop.Items.AddRange(new string[] { "localhost", "127.0.0.1" });
            if (serverName_drop.Items.Count > 0)
                serverName_drop.SelectedIndex = 0;

            serialPortStatus_bulb.On = false;
            dbServerConnStatus_bulb.On = false;

            serialPortConnection_connect_button.Enabled = true;
            serialPortConnection_disconnect_button.Enabled = false;
            serialPortConnection_connect_button.Click += ConnectClick;
            serialPortConnection_disconnect_button.Click += DisconnectClick;

            dbServerConnection_connect_button.Enabled = true;
            dbServerConnection_disconnect_button.Enabled = false;
            dbServerConnection_connect_button.Click += dbServerConnection_connect_button_Click;
            dbServerConnection_disconnect_button.Click += dbServerConnection_disconnect_button_Click;

            dbServerConnStatus_bulb.On = false;
            dbServerStatus_label.Text = "Disconnected";

            // Digital I/O Tab
            sevenSegment1.Value = "0";
            sevenSegment2.Value = "0";

            pc0.CheckedChanged += portC_CheckboxChanged;
            pc1.CheckedChanged += portC_CheckboxChanged;
            pc2.CheckedChanged += portC_CheckboxChanged;
            pc3.CheckedChanged += portC_CheckboxChanged;
            pc4.CheckedChanged += portC_CheckboxChanged;
            pc5.CheckedChanged += portC_CheckboxChanged;
            pc6.CheckedChanged += portC_CheckboxChanged;
            pc7.CheckedChanged += portC_CheckboxChanged;

            pa0_bulb.On = false;
            pa1_bulb.On = false;
            pa2_bulb.On = false;
            pa3_bulb.On = false;
            pa4_bulb.On = false;
            pa5_bulb.On = false;
            pa6_bulb.On = false;
            pa7_bulb.On = false;

            // Initialize timers
            InitializeTimers();

            // Initialize UI components
            InitializePots();
            InitializeLight();

            // Subscribe to tab change events
            tabs.SelectedIndexChanged += appTabs_SelectedIndexChanged;
        }

        #region Required Event Handlers

        // ConnectClick [Event Handler that is run when the Connect Button is clicked]
        private void ConnectClick(object sender, EventArgs e)
        {
            string port = comPort_text.SelectedItem?.ToString();
            if (port == null)
            {
                MessageBox.Show("Select a COM port first.");
                return;
            }

            int baud = (int)baudrate_text.SelectedItem;

            _appBoard.setCOMPort(port);
            _appBoard.setBaudrate(baud);

            if (_appBoard.Connect())
            {
                UpdateSerialConnectionUI();
                StartTimers();
            }
        }

        private void DisconnectClick(object sender, EventArgs e)
        {
            _appBoard.Disconnect();
            UpdateSerialConnectionUI();
            StopTimers();
        }

        // RefreshClick [Event Handler that is run when the Refresh Button is clicked (optional)]
        private void RefreshClick(object sender, EventArgs e)
        {
            // Optional refresh functionality
            if (_appBoard.IsConnected)
            {
                // Force update all readings
                UpdateTimerEvent(sender, e);
            }
        }

        // appTabs_SelectedIndexChanged [Event Handler that is run when a Tab is selected]
        private void appTabs_SelectedIndexChanged(object sender, EventArgs e)
        {
            // Handle tab selection changes
            TabControl tabs = sender as TabControl;
            if (tabs != null)
            {
                // You can add specific logic for different tabs here
                Console.WriteLine($"Tab changed to: {tabs.SelectedTab?.Text}");
            }
        }

        #endregion

        #region Timer Methods

        private void InitializeTimers()
        {
            // UpdateTimerEvent [As per the C# timer section]
            _updateTimer = new System.Windows.Forms.Timer { Interval = 100 };
            _updateTimer.Tick += UpdateTimerEvent;

            // digIO_Tick [Tick Event to control Digital I/O Functionality]
            _digIOTimer = new System.Windows.Forms.Timer { Interval = 500 };
            _digIOTimer.Tick += digIO_Tick;

            // Pot_Tick [Tick Event to control Potentiometer Functionality]
            _potTimer = new System.Windows.Forms.Timer { Interval = 200 };
            _potTimer.Tick += Pot_Tick;

            // Lamp_Tick [Tick Event to control Lamp Functionality]
            _lampTimer = new System.Windows.Forms.Timer { Interval = 500 };
            _lampTimer.Tick += Lamp_Tick;

            // Temp_Tick [Tick Event to control Temperature Control Functionality]
            _tempTimer = new System.Windows.Forms.Timer { Interval = 1000 };
            _tempTimer.Tick += Temp_Tick;
        }

        private void StartTimers()
        {
            _updateTimer?.Start();
            _digIOTimer?.Start();
            _potTimer?.Start();
            _lampTimer?.Start();
            _tempTimer?.Start();
        }

        private void StopTimers()
        {
            _updateTimer?.Stop();
            _digIOTimer?.Stop();
            _potTimer?.Stop();
            _lampTimer?.Stop();
            _tempTimer?.Stop();
        }

        // UpdateTimerEvent [As per the C# timer section]
        private void UpdateTimerEvent(object sender, EventArgs e)
        {
            UpdateSerialConnectionUI();
        }

        // digIO_Tick [Tick Event to control Digital I/O Functionality]
        private void digIO_Tick(object sender, EventArgs e)
        {
            if (!_appBoard.IsConnected) return;

            try
            {
                byte pinState = _appBoard.readPINA();
                UpdatePortALeds(pinState);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Digital I/O error: {ex.Message}");
            }
        }

        // Pot_Tick [Tick Event to control Potentiometer Functionality]
        private void Pot_Tick(object sender, EventArgs e)
        {
            if (!_appBoard.IsConnected) return;

            try
            {
                byte pot1Value = _appBoard.readPotV(1);
                byte pot2Value = _appBoard.readPotV(2);

                float pot1Voltage = ConvertAdcToVoltage(pot1Value);
                float pot2Voltage = ConvertAdcToVoltage(pot2Value);

                pot1.Value = pot1Voltage;
                pot2.Value = pot2Voltage;

                pot1_label.Text = $"Pot 1: {pot1Voltage:F2}V (ADC: {pot1Value})";
                pot2_label.Text = $"Pot 2: {pot2Voltage:F2}V (ADC: {pot2Value})";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Potentiometer error: {ex.Message}");
                pot1_label.Text = "Pot 1: Error";
                pot2_label.Text = "Pot 2: Error";
            }
        }

        // Lamp_Tick [Tick Event to control Lamp Functionality]
        private void Lamp_Tick(object sender, EventArgs e)
        {
            if (!_appBoard.IsConnected) return;

            try
            {
                byte lightValue = _appBoard.readLight();
                float lightVoltage = ConvertAdcToVoltage(lightValue);
                lightPot.Value = lightVoltage;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lamp error: {ex.Message}");
            }
        }

        // Temp_Tick [Tick Event to control Temperature Control Functionality]
        private void Temp_Tick(object sender, EventArgs e)
        {
            if (!_appBoard.IsConnected) return;

            try
            {
                byte tempValue = _appBoard.readTemp();
                float tempVoltage = ConvertAdcToVoltage(tempValue);
                // Update temperature display here if you have temperature controls
                Console.WriteLine($"Temperature: {tempVoltage:F2}V (ADC: {tempValue})");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Temperature error: {ex.Message}");
            }
        }

        #endregion

        private void UpdatePortALeds(byte portA)
        {
            pa0_bulb.On = (portA & 0b00000001) != 0;
            pa1_bulb.On = (portA & 0b00000010) != 0;
            pa2_bulb.On = (portA & 0b00000100) != 0;
            pa3_bulb.On = (portA & 0b00001000) != 0;
            pa4_bulb.On = (portA & 0b00010000) != 0;
            pa5_bulb.On = (portA & 0b00100000) != 0;
            pa6_bulb.On = (portA & 0b01000000) != 0;
            pa7_bulb.On = (portA & 0b10000000) != 0;
        }

        private void UpdateSerialConnectionUI()
        {
            bool isConnected = _appBoard.IsConnected;

            serialPortStatus_bulb.On = isConnected;
            serialPortStatus_label.Text = isConnected
              ? $"Connected to {_appBoard.GetConnectedPortName()}"
              : "Disconnected";

            serialPortConnection_connect_button.Enabled = !isConnected;
            serialPortConnection_disconnect_button.Enabled = isConnected;

            comPort_text.Enabled = !isConnected;
            baudrate_text.Enabled = !isConnected;
        }



        private void dbServerConnection_connect_button_Click(object sender, EventArgs e)
        {
            if (!isDbConnected)
            {
                string server = serverName_drop.SelectedItem?.ToString()?.Trim();
                string username = username_text.Text.Trim();
                string password = password_text.Text;
                string dbName = db_text.Text.Trim();

                if (string.IsNullOrEmpty(server) || string.IsNullOrEmpty(username) || string.IsNullOrEmpty(dbName))
                {
                    MessageBox.Show("Please fill in all required database fields.", "Missing Information", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (DatabaseManager.Instance.Connect(server, username, password, dbName, out string error))
                {
                    isDbConnected = true;
                    dbServerConnStatus_bulb.On = true;
                    dbServerStatus_label.Text = "Connected to DB";
                    dbServerConnection_connect_button.Enabled = false;
                    dbServerConnection_disconnect_button.Enabled = true;

                    serverName_drop.Enabled = false;
                    username_text.Enabled = false;
                    password_text.Enabled = false;
                    db_text.Enabled = false;
                }
                else
                {
                    MessageBox.Show($"Failed to connect to database:\n{error}", "Database Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void dbServerConnection_disconnect_button_Click(object sender, EventArgs e)
        {
            if (isDbConnected)
            {
                DatabaseManager.Instance.Disconnect();
                isDbConnected = false;
                dbServerConnStatus_bulb.On = false;
                dbServerStatus_label.Text = "Disconnected";
                dbServerConnection_connect_button.Enabled = true;
                dbServerConnection_disconnect_button.Enabled = false;

                serverName_drop.Enabled = true;
                username_text.Enabled = true;
                password_text.Enabled = true;
                db_text.Enabled = true;
            }
        }

        private void portC_CheckboxChanged(object sender, EventArgs e)
        {
            byte portValue = 0;
            if (pc0.Checked) portValue |= 0b00000001;
            if (pc1.Checked) portValue |= 0b00000010;
            if (pc2.Checked) portValue |= 0b00000100;
            if (pc3.Checked) portValue |= 0b00001000;
            if (pc4.Checked) portValue |= 0b00010000;
            if (pc5.Checked) portValue |= 0b00100000;
            if (pc6.Checked) portValue |= 0b01000000;
            if (pc7.Checked) portValue |= 0b10000000;

            sevenSegment1.Value = portValue.ToString("X2")[0].ToString();
            sevenSegment2.Value = portValue.ToString("X2")[1].ToString();

            if (_appBoard.IsConnected)
            {
                try
                {
                    _appBoard.writePORTC(portValue);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error writing to PORTC: {ex.Message}");
                }
            }
        }

        // FormClosing [Event Handler that is run when the form is closing, to reset all MCU peripherals]
        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            base.OnFormClosing(e);

            // Reset all MCU peripherals
            if (_appBoard.IsConnected)
            {
                try
                {
                    _appBoard.writePORTC(0);     // Reset PORTC
                    _appBoard.writeLight(0);     // Turn off light
                    _appBoard.writeHeater(0);    // Turn off heater
                    _appBoard.writeMotor(0);     // Turn off motor
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error resetting MCU peripherals: {ex.Message}");
                }
            }

            StopTimers();
            _appBoard.Disconnect();
        }


        private void InitializePots()
        {
            // Configure pot gauges
            ConfigurePotGauge(pot1, "Pot 1");
            ConfigurePotGauge(pot2, "Pot 2");
        }

        private static void ConfigurePotGauge(AquaControls.AquaGauge gauge, string dialText)
        {
            gauge.MinValue = 0f;
            gauge.MaxValue = 5f; // 5V max
            gauge.DialText = dialText;
            gauge.Value = 0f;

            // Set gauge appearance
            gauge.RecommendedValue = 2.5f; // Middle value
            gauge.ThresholdPercent = 80f; // Warning at 80%
        }

        private static float ConvertAdcToVoltage(byte adcValue)
        {
            // Convert 8-bit ADC value (0-255) to voltage (0-5V)
            return adcValue / 255f * 5f;
        }

        private void InitializeLight()
        {
            // Configure light gauge
            lightPot.MinValue = 0f;
            lightPot.MaxValue = 5f;
            lightPot.DialText = "Light Level";
            lightPot.Value = 0f;
            lightPot.ThresholdPercent = 80f;

            // Configure scroll bar
            lightPot_scroll.Minimum = 0;
            lightPot_scroll.Maximum = 264; // 255 + width of bar 9
            lightPot_scroll.Value = 0;
            lightPot_scroll.ValueChanged += LightPot_scroll_ValueChanged;

            // Configure text box to show scroll bar value
            lightPot_text.Text = "0";
            lightPot_text.ReadOnly = true;
        }



        private void LightPot_scroll_ValueChanged(object? sender, EventArgs e)
        {
            try
            {
                int scrollValue = lightPot_scroll.Value;
                lightPot_text.Text = scrollValue.ToString();

                // Send the scroll bar value to MCU to adjust lamp brightness
                if (_appBoard.IsConnected)
                {
                    _appBoard.writeLight((ushort)scrollValue);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error setting light: {ex.Message}", "Light Control Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }


    }
}
