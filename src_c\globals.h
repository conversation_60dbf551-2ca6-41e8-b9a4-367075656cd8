#ifndef GLOBALS_H
#define GL<PERSON><PERSON>LS_H

#include <stdint.h>
#include <stdbool.h>

// Instruction codes 
typedef enum {
  TXCHECK     = 0x00,  // Return 0x0F to indicate communication is working
  READ_PINA   = 0x01,  // Return contents of PINA
  READ_POT1   = 0x02,  // Read ADC Channel 2 and return ADCH
  READ_POT2   = 0x03,  // Read ADC Channel 1 and return ADCH
  READ_TEMP   = 0x04,  // Read ADC Channel 3 and return ADCH
  READ_LIGHT  = 0x05,  // Read ADC Channel 0 and return ADCH

  SET_PORTC   = 0x0A,  // Write LSB of received int to PORTC, return 0x0A
  SET_HEATER  = 0x0B,  // Write received int to OCR1C, return 0x0B
  SET_LIGHT   = 0x0C,  // Write received int to OCR1B, return 0x0C
  SET_MOTOR   = 0x0D,  // Write received int to OCR1A, return 0x0D

  NONE        = 0xFF   // No instructions currently
} InstructionCode;

// System states
typedef enum {
  AWAIT,
  RECEIVING,
  PROCESS_SHORT_INSTRUCTION,
  PROCESS_LONG_INSTRUCTION
} SystemState;


// Instruction handling
extern volatile InstructionCode instruction_byte;
extern volatile uint8_t instruction_data_ls_byte;
extern volatile uint8_t instruction_data_ms_byte;

// UART transmission
extern volatile uint8_t uart_tx_byte_pending;
extern volatile bool is_uart_tx_byte_pending;

// UART reception
extern unsigned char received_buffer[8];
extern volatile bool is_UART_receiving;

// ADC
extern uint8_t adc_ls_byte;
extern uint8_t adc_ms_byte;
extern volatile bool is_adc_complete;
extern volatile bool is_adc_running;

// System state
extern volatile SystemState state;

// Input change tracking
extern volatile bool has_switch_input_changed;
extern volatile bool has_ADC_input_changed;


#endif