using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Threading;
using System.Windows.Forms;

namespace front
{
    public class AppBoard
    {
        private static readonly Lazy<AppBoard> _instance = new(() => new AppBoard());
        public static AppBoard Instance => _instance.Value;

        private SerialPort _serialPort;
        private readonly object _lock = new();
        private readonly AutoResetEvent _responseReady = new(false);
        private byte _lastResponse;
        private bool _awaitingResponse;

        private const byte START_BYTE = 0x53;
        private const byte STOP_BYTE = 0xAA;

        private string _selectedCOMPort = "";
        private int _selectedBaudrate = 9600;

        private AppBoard() { }

        public bool Connect()
        {
            try
            {
                if (_serialPort != null && _serialPort.IsOpen)
                    _serialPort.Close();

                _serialPort = new SerialPort(_selectedCOMPort, _selectedBaudrate)
                {
                    // Configure as per specification: 8N1, 500ms timeouts
                    DataBits = 8,
                    Parity = Parity.None,
                    StopBits = StopBits.One,
                    ReadTimeout = 500,
                    WriteTimeout = 500,
                    Handshake = Handshake.None
                };
                _serialPort.DataReceived += SerialPort_DataReceived;
                _serialPort.Open();
                return true;
            }
            catch (Exception ex)
            {
                ShowError("Serial Connection Failed", ex.Message);
                return false;
            }
        }

        public void Disconnect()
        {
            try
            {
                if (_serialPort != null)
                {
                    if (_serialPort.IsOpen)
                        _serialPort.Close();
                    _serialPort.DataReceived -= SerialPort_DataReceived;
                    _serialPort.Dispose();
                    _serialPort = null;
                }
            }
            catch (Exception ex)
            {
                ShowError("Serial Disconnect Failed", ex.Message);
            }
        }

        public string[] getCOMPorts()
        {
            return SerialPort.GetPortNames();
        }

        public void setCOMPort(string port)
        {
            _selectedCOMPort = port;
        }

        public void setBaudrate(int baudrate)
        {
            _selectedBaudrate = baudrate;
        }

        public bool IsConnected => _serialPort != null && _serialPort.IsOpen;

        public string GetConnectedPortName()
        {
            return _serialPort?.IsOpen == true ? _serialPort.PortName : "N/A";
        }

        public byte checkTx()
        {
            return readUInt8(0x00);
        }

        public byte readPINA()
        {
            return readUInt8(0x01);
        }

        public byte readPotV(int potNumber)
        {
            if (potNumber == 1)
                return readUInt8(0x02);
            else if (potNumber == 2)
                return readUInt8(0x03);
            else
                throw new ArgumentException("Pot number must be 1 or 2");
        }

        public byte readTemp()
        {
            return readUInt8(0x04);
        }

        public byte readLight()
        {
            return readUInt8(0x05);
        }

        public void writePORTC(ushort value)
        {
            writeUInt16(0x0A, value);
        }

        public void writeHeater(ushort value)
        {
            writeUInt16(0x0B, value);
        }

        public void writeLight(ushort value)
        {
            writeUInt16(0x0C, value);
        }

        public void writeMotor(ushort value)
        {
            writeUInt16(0x0D, value);
        }

        public byte readUInt8(byte instruction)
        {
            lock (_lock)
            {
                try
                {
                    List<byte> packet = new() { START_BYTE, instruction, STOP_BYTE };
                    _awaitingResponse = true;
                    _serialPort.Write(packet.ToArray(), 0, packet.Count);

                    if (_responseReady.WaitOne(200))
                    {
                        return _lastResponse;
                    }
                    else
                    {
                        throw new TimeoutException("No response from MCU.");
                    }
                }
                catch (Exception ex)
                {
                    ShowError("Serial Read Failed", ex.Message);
                    throw;
                }
            }
        }

        public byte writeUInt16(byte instruction, ushort data)
        {
            lock (_lock)
            {
                try
                {
                    List<byte> packet = new() { START_BYTE, instruction };
                    packet.Add((byte)(data & 0xFF));       // LSB
                    packet.Add((byte)((data >> 8) & 0xFF)); // MSB
                    packet.Add(STOP_BYTE);

                    _awaitingResponse = true;
                    _serialPort.Write(packet.ToArray(), 0, packet.Count);

                    if (_responseReady.WaitOne(200))
                    {
                        return _lastResponse;
                    }
                    else
                    {
                        throw new TimeoutException("No response from MCU.");
                    }
                }
                catch (Exception ex)
                {
                    ShowError("Serial Write Failed", ex.Message);
                    throw;
                }
            }
        }

        private void SerialPort_DataReceived(object sender, SerialDataReceivedEventArgs e)
        {
            try
            {
                if (_awaitingResponse && _serialPort.BytesToRead > 0)
                {
                    _lastResponse = (byte)_serialPort.ReadByte();
                    _awaitingResponse = false;
                    _responseReady.Set();
                }
            }
            catch (Exception ex)
            {
                ShowError("Serial Read Failed", ex.Message);
            }
        }

        private void ShowError(string title, string msg)
        {
            MessageBox.Show(msg, title, MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        public static int[] GetBaudRates() => new int[] { 9600, 19200, 38400, 57600, 115200 };
    }
}
