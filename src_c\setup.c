#include <avr/io.h>
#include <avr/interrupt.h>

#include "globals.h"
#include "aut_utils.h"
#include "setup.h"

#include "adc.h"
#include "timer.h"
#include "uart.h"

void setup() {
  adc_setup();
  timer_setup();
  uart_setup();

  // PORTE (MUX config for PORTA input)
  DDRE = 
    (1 << PE0) |
    (1 << PE1);
  PORTE = 
    (1 << PE0);  // Set PE0 high for PORTA MUX

  // PORTA (all inputs for switches)
  DDRA = 0x00;

  // PORTB (PWM output PINs)
  DDRB = 
    (1 << PB5) |
    (1 << PB6) |
    (1 << PB7);

  // PORTC (all outputs for LEDs)
  DDRC   = 0xFF;
  PORTC  = 0x00;

  sei();
}
