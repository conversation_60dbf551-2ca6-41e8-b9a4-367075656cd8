﻿
namespace front
{
    partial class MainForm
    {
        /// <summary>
        ///  Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        ///  Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        ///  Required method for Designer support - do not modify
        ///  the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            tabs = new TabControl();
            setup = new TabPage();
            setup_main_panel = new TableLayoutPanel();
            serialPortConnection_group = new GroupBox();
            serialPortConnection_panel = new TableLayoutPanel();
            serialPortStatus_panel = new TableLayoutPanel();
            serialPortStatus_label = new Label();
            serialPortStatus_bulb = new Bulb.LedBulb();
            serialPortConnectionSettings_panel = new TableLayoutPanel();
            comPort_label = new Label();
            baudrate_label = new Label();
            comPort_text = new ComboBox();
            baudrate_text = new ComboBox();
            serialPortConnectionButtons_panel = new TableLayoutPanel();
            serialPortConnection_connect_button = new Button();
            serialPortConnection_disconnect_button = new Button();
            databaseServerConnection_group = new GroupBox();
            dbServerConn_panel = new TableLayoutPanel();
            dbServerConnSettings_panel = new TableLayoutPanel();
            serverName_label = new Label();
            serverName_drop = new ComboBox();
            username_text = new TextBox();
            username_label = new Label();
            password_label = new Label();
            db_label = new Label();
            db_text = new TextBox();
            password_text = new TextBox();
            dbServerConnectionButtons_panel = new TableLayoutPanel();
            dbServerConnection_connect_button = new Button();
            dbServerConnection_disconnect_button = new Button();
            databaseServerStatus_panel = new TableLayoutPanel();
            dbServerStatus_label = new Label();
            dbServerConnStatus_bulb = new Bulb.LedBulb();
            digital = new TabPage();
            digital_main_panel = new TableLayoutPanel();
            pinA_group = new GroupBox();
            pinA_panel = new TableLayoutPanel();
            pa0_label = new Label();
            pa1_label = new Label();
            pa2_label = new Label();
            pa3_label = new Label();
            pa4_label = new Label();
            pa5_label = new Label();
            pa6_label = new Label();
            pa7_label = new Label();
            pa0_bulb = new Bulb.LedBulb();
            pa1_bulb = new Bulb.LedBulb();
            pa2_bulb = new Bulb.LedBulb();
            pa3_bulb = new Bulb.LedBulb();
            pa4_bulb = new Bulb.LedBulb();
            pa5_bulb = new Bulb.LedBulb();
            pa6_bulb = new Bulb.LedBulb();
            pa7_bulb = new Bulb.LedBulb();
            portC_group = new GroupBox();
            portC_panel = new TableLayoutPanel();
            portCCheckbox_panel = new TableLayoutPanel();
            pc0 = new CheckBox();
            pc1 = new CheckBox();
            pc2 = new CheckBox();
            pc3 = new CheckBox();
            pc4 = new CheckBox();
            pc5 = new CheckBox();
            pc6 = new CheckBox();
            pc7 = new CheckBox();
            sevenSegment1 = new DmitryBrant.CustomControls.SevenSegment();
            sevenSegment2 = new DmitryBrant.CustomControls.SevenSegment();
            lights = new TabPage();
            lights_main_panel = new TableLayoutPanel();
            lights_ports_group = new GroupBox();
            potPorts_panel = new TableLayoutPanel();
            pot1_label = new Label();
            pot2_label = new Label();
            pot1 = new AquaControls.AquaGauge();
            pot2 = new AquaControls.AquaGauge();
            light_lights_group = new GroupBox();
            tableLayoutPanel2 = new TableLayoutPanel();
            lightPot = new AquaControls.AquaGauge();
            lightPot_panel = new TableLayoutPanel();
            lightPot_scroll = new VScrollBar();
            lightPot_text = new TextBox();
            temp = new TabPage();
            temp_main_panel = new TableLayoutPanel();
            control_group = new GroupBox();
            control_panel = new TableLayoutPanel();
            controlSettings_panel = new TableLayoutPanel();
            setPointTemp_panel = new TableLayoutPanel();
            setPointTemp_label = new Label();
            setPointTemp_upDown = new NumericUpDown();
            pITuning_panel = new TableLayoutPanel();
            pITuning_label = new Label();
            pITuningKValues_panel = new TableLayoutPanel();
            pITuningKP_label = new Label();
            pITuningKI_label = new Label();
            pITuningKP_upDown = new NumericUpDown();
            pITuningKI_upDown = new NumericUpDown();
            tempMiscValues_panel = new TableLayoutPanel();
            actualTemp_label = new Label();
            motorSpeed_label = new Label();
            actualTemp_text = new TextBox();
            motorSpeed_text = new TextBox();
            cloudDataLogging_group = new GroupBox();
            cloudDataLogging_panel = new TableLayoutPanel();
            manualDataLogging_group = new GroupBox();
            manualDataLogging_panel = new TableLayoutPanel();
            manualDataLogging_text = new TextBox();
            manualDataLoggingInsert_button = new Button();
            autoDataLogging_group = new GroupBox();
            autoDataLogging_panel = new TableLayoutPanel();
            autoDataLoggingEnable_button = new Button();
            autoDataLoggingDisable_button = new Button();
            autoDataLoggingStatus_label = new Label();
            tabs.SuspendLayout();
            setup.SuspendLayout();
            setup_main_panel.SuspendLayout();
            serialPortConnection_group.SuspendLayout();
            serialPortConnection_panel.SuspendLayout();
            serialPortStatus_panel.SuspendLayout();
            serialPortConnectionSettings_panel.SuspendLayout();
            serialPortConnectionButtons_panel.SuspendLayout();
            databaseServerConnection_group.SuspendLayout();
            dbServerConn_panel.SuspendLayout();
            dbServerConnSettings_panel.SuspendLayout();
            dbServerConnectionButtons_panel.SuspendLayout();
            databaseServerStatus_panel.SuspendLayout();
            digital.SuspendLayout();
            digital_main_panel.SuspendLayout();
            pinA_group.SuspendLayout();
            pinA_panel.SuspendLayout();
            portC_group.SuspendLayout();
            portC_panel.SuspendLayout();
            portCCheckbox_panel.SuspendLayout();
            lights.SuspendLayout();
            lights_main_panel.SuspendLayout();
            lights_ports_group.SuspendLayout();
            potPorts_panel.SuspendLayout();
            light_lights_group.SuspendLayout();
            tableLayoutPanel2.SuspendLayout();
            lightPot_panel.SuspendLayout();
            temp.SuspendLayout();
            temp_main_panel.SuspendLayout();
            control_group.SuspendLayout();
            control_panel.SuspendLayout();
            controlSettings_panel.SuspendLayout();
            setPointTemp_panel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)setPointTemp_upDown).BeginInit();
            pITuning_panel.SuspendLayout();
            pITuningKValues_panel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)pITuningKP_upDown).BeginInit();
            ((System.ComponentModel.ISupportInitialize)pITuningKI_upDown).BeginInit();
            tempMiscValues_panel.SuspendLayout();
            cloudDataLogging_group.SuspendLayout();
            cloudDataLogging_panel.SuspendLayout();
            manualDataLogging_group.SuspendLayout();
            manualDataLogging_panel.SuspendLayout();
            autoDataLogging_group.SuspendLayout();
            autoDataLogging_panel.SuspendLayout();
            SuspendLayout();
            // 
            // tabs
            // 
            tabs.Controls.Add(setup);
            tabs.Controls.Add(digital);
            tabs.Controls.Add(lights);
            tabs.Controls.Add(temp);
            tabs.Dock = DockStyle.Fill;
            tabs.Location = new Point(0, 0);
            tabs.MaximumSize = new Size(500, 600);
            tabs.Name = "tabs";
            tabs.SelectedIndex = 0;
            tabs.Size = new Size(484, 561);
            tabs.TabIndex = 0;
            // 
            // setup
            // 
            setup.Controls.Add(setup_main_panel);
            setup.Location = new Point(4, 24);
            setup.Name = "setup";
            setup.Padding = new Padding(3);
            setup.Size = new Size(476, 533);
            setup.TabIndex = 0;
            setup.Text = "Setup";
            setup.UseVisualStyleBackColor = true;
            // 
            // setup_main_panel
            // 
            setup_main_panel.AutoSize = true;
            setup_main_panel.AutoSizeMode = AutoSizeMode.GrowAndShrink;
            setup_main_panel.ColumnCount = 1;
            setup_main_panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            setup_main_panel.Controls.Add(serialPortConnection_group, 0, 0);
            setup_main_panel.Controls.Add(databaseServerConnection_group, 0, 1);
            setup_main_panel.Dock = DockStyle.Fill;
            setup_main_panel.Location = new Point(3, 3);
            setup_main_panel.Margin = new Padding(0);
            setup_main_panel.Name = "setup_main_panel";
            setup_main_panel.RowCount = 2;
            setup_main_panel.RowStyles.Add(new RowStyle());
            setup_main_panel.RowStyles.Add(new RowStyle());
            setup_main_panel.Size = new Size(470, 527);
            setup_main_panel.TabIndex = 2;
            // 
            // serialPortConnection_group
            // 
            serialPortConnection_group.AutoSize = true;
            serialPortConnection_group.AutoSizeMode = AutoSizeMode.GrowAndShrink;
            serialPortConnection_group.Controls.Add(serialPortConnection_panel);
            serialPortConnection_group.Dock = DockStyle.Top;
            serialPortConnection_group.Location = new Point(3, 3);
            serialPortConnection_group.Name = "serialPortConnection_group";
            serialPortConnection_group.Size = new Size(464, 138);
            serialPortConnection_group.TabIndex = 0;
            serialPortConnection_group.TabStop = false;
            serialPortConnection_group.Text = "Serial Port Connection";
            // 
            // serialPortConnection_panel
            // 
            serialPortConnection_panel.AutoSize = true;
            serialPortConnection_panel.AutoSizeMode = AutoSizeMode.GrowAndShrink;
            serialPortConnection_panel.ColumnCount = 1;
            serialPortConnection_panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            serialPortConnection_panel.Controls.Add(serialPortStatus_panel, 0, 2);
            serialPortConnection_panel.Controls.Add(serialPortConnectionSettings_panel, 0, 0);
            serialPortConnection_panel.Controls.Add(serialPortConnectionButtons_panel, 0, 1);
            serialPortConnection_panel.Dock = DockStyle.Top;
            serialPortConnection_panel.Location = new Point(3, 19);
            serialPortConnection_panel.Margin = new Padding(0);
            serialPortConnection_panel.Name = "serialPortConnection_panel";
            serialPortConnection_panel.RowCount = 3;
            serialPortConnection_panel.RowStyles.Add(new RowStyle());
            serialPortConnection_panel.RowStyles.Add(new RowStyle());
            serialPortConnection_panel.RowStyles.Add(new RowStyle());
            serialPortConnection_panel.Size = new Size(458, 116);
            serialPortConnection_panel.TabIndex = 0;
            // 
            // serialPortStatus_panel
            // 
            serialPortStatus_panel.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            serialPortStatus_panel.AutoSize = true;
            serialPortStatus_panel.AutoSizeMode = AutoSizeMode.GrowAndShrink;
            serialPortStatus_panel.ColumnCount = 2;
            serialPortStatus_panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            serialPortStatus_panel.ColumnStyles.Add(new ColumnStyle());
            serialPortStatus_panel.Controls.Add(serialPortStatus_label, 0, 0);
            serialPortStatus_panel.Controls.Add(serialPortStatus_bulb, 1, 0);
            serialPortStatus_panel.Location = new Point(0, 87);
            serialPortStatus_panel.Margin = new Padding(0);
            serialPortStatus_panel.Name = "serialPortStatus_panel";
            serialPortStatus_panel.RowCount = 1;
            serialPortStatus_panel.RowStyles.Add(new RowStyle());
            serialPortStatus_panel.Size = new Size(458, 29);
            serialPortStatus_panel.TabIndex = 3;
            // 
            // serialPortStatus_label
            // 
            serialPortStatus_label.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            serialPortStatus_label.AutoSize = true;
            serialPortStatus_label.Location = new Point(3, 7);
            serialPortStatus_label.Name = "serialPortStatus_label";
            serialPortStatus_label.Size = new Size(423, 15);
            serialPortStatus_label.TabIndex = 0;
            serialPortStatus_label.Text = "Serial Port Status";
            // 
            // serialPortStatus_bulb
            // 
            serialPortStatus_bulb.Anchor = AnchorStyles.Right;
            serialPortStatus_bulb.Location = new Point(432, 3);
            serialPortStatus_bulb.Name = "serialPortStatus_bulb";
            serialPortStatus_bulb.On = true;
            serialPortStatus_bulb.RightToLeft = RightToLeft.No;
            serialPortStatus_bulb.Size = new Size(23, 23);
            serialPortStatus_bulb.TabIndex = 1;
            serialPortStatus_bulb.Text = "ledBulb1";
            // 
            // serialPortConnectionSettings_panel
            // 
            serialPortConnectionSettings_panel.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            serialPortConnectionSettings_panel.AutoSize = true;
            serialPortConnectionSettings_panel.AutoSizeMode = AutoSizeMode.GrowAndShrink;
            serialPortConnectionSettings_panel.ColumnCount = 2;
            serialPortConnectionSettings_panel.ColumnStyles.Add(new ColumnStyle());
            serialPortConnectionSettings_panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            serialPortConnectionSettings_panel.Controls.Add(comPort_label, 0, 0);
            serialPortConnectionSettings_panel.Controls.Add(baudrate_label, 0, 1);
            serialPortConnectionSettings_panel.Controls.Add(comPort_text, 1, 0);
            serialPortConnectionSettings_panel.Controls.Add(baudrate_text, 1, 1);
            serialPortConnectionSettings_panel.Location = new Point(0, 0);
            serialPortConnectionSettings_panel.Margin = new Padding(0);
            serialPortConnectionSettings_panel.Name = "serialPortConnectionSettings_panel";
            serialPortConnectionSettings_panel.RowCount = 2;
            serialPortConnectionSettings_panel.RowStyles.Add(new RowStyle());
            serialPortConnectionSettings_panel.RowStyles.Add(new RowStyle());
            serialPortConnectionSettings_panel.Size = new Size(458, 58);
            serialPortConnectionSettings_panel.TabIndex = 0;
            // 
            // comPort_label
            // 
            comPort_label.Anchor = AnchorStyles.Left;
            comPort_label.AutoSize = true;
            comPort_label.Font = new Font("Segoe UI", 11.25F, FontStyle.Bold, GraphicsUnit.Point, 0);
            comPort_label.Location = new Point(3, 4);
            comPort_label.Name = "comPort_label";
            comPort_label.Size = new Size(77, 20);
            comPort_label.TabIndex = 0;
            comPort_label.Text = "COM Port";
            // 
            // baudrate_label
            // 
            baudrate_label.Anchor = AnchorStyles.Left;
            baudrate_label.AutoSize = true;
            baudrate_label.Font = new Font("Segoe UI", 11.25F, FontStyle.Bold, GraphicsUnit.Point, 0);
            baudrate_label.Location = new Point(3, 33);
            baudrate_label.Name = "baudrate_label";
            baudrate_label.Size = new Size(73, 20);
            baudrate_label.TabIndex = 1;
            baudrate_label.Text = "Baudrate";
            // 
            // comPort_text
            // 
            comPort_text.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            comPort_text.FormattingEnabled = true;
            comPort_text.Location = new Point(86, 3);
            comPort_text.Name = "comPort_text";
            comPort_text.Size = new Size(369, 23);
            comPort_text.TabIndex = 2;
            // 
            // baudrate_text
            // 
            baudrate_text.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            baudrate_text.FormattingEnabled = true;
            baudrate_text.Location = new Point(86, 32);
            baudrate_text.Name = "baudrate_text";
            baudrate_text.Size = new Size(369, 23);
            baudrate_text.TabIndex = 3;
            // 
            // serialPortConnectionButtons_panel
            // 
            serialPortConnectionButtons_panel.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            serialPortConnectionButtons_panel.AutoSize = true;
            serialPortConnectionButtons_panel.AutoSizeMode = AutoSizeMode.GrowAndShrink;
            serialPortConnectionButtons_panel.ColumnCount = 2;
            serialPortConnectionButtons_panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            serialPortConnectionButtons_panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            serialPortConnectionButtons_panel.Controls.Add(serialPortConnection_connect_button, 0, 0);
            serialPortConnectionButtons_panel.Controls.Add(serialPortConnection_disconnect_button, 1, 0);
            serialPortConnectionButtons_panel.Location = new Point(0, 58);
            serialPortConnectionButtons_panel.Margin = new Padding(0);
            serialPortConnectionButtons_panel.Name = "serialPortConnectionButtons_panel";
            serialPortConnectionButtons_panel.RowCount = 1;
            serialPortConnectionButtons_panel.RowStyles.Add(new RowStyle(SizeType.Percent, 50F));
            serialPortConnectionButtons_panel.Size = new Size(458, 29);
            serialPortConnectionButtons_panel.TabIndex = 1;
            // 
            // serialPortConnection_connect_button
            // 
            serialPortConnection_connect_button.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            serialPortConnection_connect_button.Location = new Point(3, 3);
            serialPortConnection_connect_button.Name = "serialPortConnection_connect_button";
            serialPortConnection_connect_button.Size = new Size(223, 23);
            serialPortConnection_connect_button.TabIndex = 0;
            serialPortConnection_connect_button.Text = "Connect";
            serialPortConnection_connect_button.UseVisualStyleBackColor = true;
            // 
            // serialPortConnection_disconnect_button
            // 
            serialPortConnection_disconnect_button.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            serialPortConnection_disconnect_button.Location = new Point(232, 3);
            serialPortConnection_disconnect_button.Name = "serialPortConnection_disconnect_button";
            serialPortConnection_disconnect_button.Size = new Size(223, 23);
            serialPortConnection_disconnect_button.TabIndex = 1;
            serialPortConnection_disconnect_button.Text = "Disconnect";
            serialPortConnection_disconnect_button.UseVisualStyleBackColor = true;
            // 
            // databaseServerConnection_group
            // 
            databaseServerConnection_group.AutoSize = true;
            databaseServerConnection_group.AutoSizeMode = AutoSizeMode.GrowAndShrink;
            databaseServerConnection_group.Controls.Add(dbServerConn_panel);
            databaseServerConnection_group.Dock = DockStyle.Top;
            databaseServerConnection_group.Location = new Point(3, 147);
            databaseServerConnection_group.Name = "databaseServerConnection_group";
            databaseServerConnection_group.Size = new Size(464, 196);
            databaseServerConnection_group.TabIndex = 1;
            databaseServerConnection_group.TabStop = false;
            databaseServerConnection_group.Text = "Database Server Connection";
            // 
            // dbServerConn_panel
            // 
            dbServerConn_panel.AutoSize = true;
            dbServerConn_panel.AutoSizeMode = AutoSizeMode.GrowAndShrink;
            dbServerConn_panel.ColumnCount = 1;
            dbServerConn_panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            dbServerConn_panel.Controls.Add(dbServerConnSettings_panel, 0, 0);
            dbServerConn_panel.Controls.Add(dbServerConnectionButtons_panel, 0, 1);
            dbServerConn_panel.Controls.Add(databaseServerStatus_panel, 0, 2);
            dbServerConn_panel.Dock = DockStyle.Fill;
            dbServerConn_panel.Location = new Point(3, 19);
            dbServerConn_panel.Margin = new Padding(0);
            dbServerConn_panel.Name = "dbServerConn_panel";
            dbServerConn_panel.RowCount = 3;
            dbServerConn_panel.RowStyles.Add(new RowStyle());
            dbServerConn_panel.RowStyles.Add(new RowStyle());
            dbServerConn_panel.RowStyles.Add(new RowStyle());
            dbServerConn_panel.Size = new Size(458, 174);
            dbServerConn_panel.TabIndex = 0;
            // 
            // dbServerConnSettings_panel
            // 
            dbServerConnSettings_panel.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            dbServerConnSettings_panel.AutoSize = true;
            dbServerConnSettings_panel.AutoSizeMode = AutoSizeMode.GrowAndShrink;
            dbServerConnSettings_panel.ColumnCount = 2;
            dbServerConnSettings_panel.ColumnStyles.Add(new ColumnStyle());
            dbServerConnSettings_panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            dbServerConnSettings_panel.Controls.Add(serverName_label, 0, 0);
            dbServerConnSettings_panel.Controls.Add(serverName_drop, 1, 0);
            dbServerConnSettings_panel.Controls.Add(username_text, 1, 1);
            dbServerConnSettings_panel.Controls.Add(username_label, 0, 1);
            dbServerConnSettings_panel.Controls.Add(password_label, 0, 2);
            dbServerConnSettings_panel.Controls.Add(db_label, 0, 3);
            dbServerConnSettings_panel.Controls.Add(db_text, 1, 3);
            dbServerConnSettings_panel.Controls.Add(password_text, 1, 2);
            dbServerConnSettings_panel.Location = new Point(0, 0);
            dbServerConnSettings_panel.Margin = new Padding(0);
            dbServerConnSettings_panel.Name = "dbServerConnSettings_panel";
            dbServerConnSettings_panel.RowCount = 4;
            dbServerConnSettings_panel.RowStyles.Add(new RowStyle());
            dbServerConnSettings_panel.RowStyles.Add(new RowStyle());
            dbServerConnSettings_panel.RowStyles.Add(new RowStyle());
            dbServerConnSettings_panel.RowStyles.Add(new RowStyle());
            dbServerConnSettings_panel.Size = new Size(458, 116);
            dbServerConnSettings_panel.TabIndex = 0;
            // 
            // serverName_label
            // 
            serverName_label.Anchor = AnchorStyles.Left;
            serverName_label.AutoSize = true;
            serverName_label.Font = new Font("Segoe UI", 11.25F, FontStyle.Bold, GraphicsUnit.Point, 0);
            serverName_label.Location = new Point(3, 4);
            serverName_label.Name = "serverName_label";
            serverName_label.Size = new Size(100, 20);
            serverName_label.TabIndex = 0;
            serverName_label.Text = "Server Name";
            // 
            // serverName_drop
            // 
            serverName_drop.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            serverName_drop.FormattingEnabled = true;
            serverName_drop.Location = new Point(109, 3);
            serverName_drop.Name = "serverName_drop";
            serverName_drop.Size = new Size(346, 23);
            serverName_drop.TabIndex = 1;
            // 
            // username_text
            // 
            username_text.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            username_text.Location = new Point(109, 32);
            username_text.Name = "username_text";
            username_text.Size = new Size(346, 23);
            username_text.TabIndex = 2;
            // 
            // username_label
            // 
            username_label.Anchor = AnchorStyles.Left;
            username_label.AutoSize = true;
            username_label.Font = new Font("Segoe UI", 11.25F, FontStyle.Bold, GraphicsUnit.Point, 0);
            username_label.Location = new Point(3, 33);
            username_label.Name = "username_label";
            username_label.Size = new Size(80, 20);
            username_label.TabIndex = 5;
            username_label.Text = "Username";
            // 
            // password_label
            // 
            password_label.Anchor = AnchorStyles.Left;
            password_label.AutoSize = true;
            password_label.Font = new Font("Segoe UI", 11.25F, FontStyle.Bold, GraphicsUnit.Point, 0);
            password_label.Location = new Point(3, 62);
            password_label.Name = "password_label";
            password_label.Size = new Size(76, 20);
            password_label.TabIndex = 6;
            password_label.Text = "Password";
            // 
            // db_label
            // 
            db_label.Anchor = AnchorStyles.Left;
            db_label.AutoSize = true;
            db_label.Font = new Font("Segoe UI", 11.25F, FontStyle.Bold, GraphicsUnit.Point, 0);
            db_label.Location = new Point(3, 91);
            db_label.Name = "db_label";
            db_label.Size = new Size(74, 20);
            db_label.TabIndex = 7;
            db_label.Text = "Database";
            // 
            // db_text
            // 
            db_text.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            db_text.Location = new Point(109, 90);
            db_text.Name = "db_text";
            db_text.Size = new Size(346, 23);
            db_text.TabIndex = 9;
            // 
            // password_text
            // 
            password_text.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            password_text.Location = new Point(109, 61);
            password_text.Name = "password_text";
            password_text.Size = new Size(346, 23);
            password_text.TabIndex = 8;
            // 
            // dbServerConnectionButtons_panel
            // 
            dbServerConnectionButtons_panel.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            dbServerConnectionButtons_panel.AutoSize = true;
            dbServerConnectionButtons_panel.AutoSizeMode = AutoSizeMode.GrowAndShrink;
            dbServerConnectionButtons_panel.ColumnCount = 2;
            dbServerConnectionButtons_panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            dbServerConnectionButtons_panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            dbServerConnectionButtons_panel.Controls.Add(dbServerConnection_connect_button, 0, 0);
            dbServerConnectionButtons_panel.Controls.Add(dbServerConnection_disconnect_button, 1, 0);
            dbServerConnectionButtons_panel.Location = new Point(0, 116);
            dbServerConnectionButtons_panel.Margin = new Padding(0);
            dbServerConnectionButtons_panel.Name = "dbServerConnectionButtons_panel";
            dbServerConnectionButtons_panel.RowCount = 1;
            dbServerConnectionButtons_panel.RowStyles.Add(new RowStyle());
            dbServerConnectionButtons_panel.RowStyles.Add(new RowStyle(SizeType.Absolute, 20F));
            dbServerConnectionButtons_panel.Size = new Size(458, 29);
            dbServerConnectionButtons_panel.TabIndex = 1;
            // 
            // dbServerConnection_connect_button
            // 
            dbServerConnection_connect_button.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            dbServerConnection_connect_button.Location = new Point(3, 3);
            dbServerConnection_connect_button.Name = "dbServerConnection_connect_button";
            dbServerConnection_connect_button.Size = new Size(223, 23);
            dbServerConnection_connect_button.TabIndex = 0;
            dbServerConnection_connect_button.Text = "Database Connect";
            dbServerConnection_connect_button.UseVisualStyleBackColor = true;
            // 
            // dbServerConnection_disconnect_button
            // 
            dbServerConnection_disconnect_button.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            dbServerConnection_disconnect_button.Location = new Point(232, 3);
            dbServerConnection_disconnect_button.Name = "dbServerConnection_disconnect_button";
            dbServerConnection_disconnect_button.Size = new Size(223, 23);
            dbServerConnection_disconnect_button.TabIndex = 1;
            dbServerConnection_disconnect_button.Text = "Database Disconnect";
            dbServerConnection_disconnect_button.UseVisualStyleBackColor = true;
            // 
            // databaseServerStatus_panel
            // 
            databaseServerStatus_panel.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            databaseServerStatus_panel.AutoSize = true;
            databaseServerStatus_panel.AutoSizeMode = AutoSizeMode.GrowAndShrink;
            databaseServerStatus_panel.ColumnCount = 2;
            databaseServerStatus_panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            databaseServerStatus_panel.ColumnStyles.Add(new ColumnStyle());
            databaseServerStatus_panel.Controls.Add(dbServerStatus_label, 0, 0);
            databaseServerStatus_panel.Controls.Add(dbServerConnStatus_bulb, 1, 0);
            databaseServerStatus_panel.Location = new Point(0, 145);
            databaseServerStatus_panel.Margin = new Padding(0);
            databaseServerStatus_panel.Name = "databaseServerStatus_panel";
            databaseServerStatus_panel.RowCount = 1;
            databaseServerStatus_panel.RowStyles.Add(new RowStyle());
            databaseServerStatus_panel.Size = new Size(458, 29);
            databaseServerStatus_panel.TabIndex = 2;
            // 
            // dbServerStatus_label
            // 
            dbServerStatus_label.Anchor = AnchorStyles.Left;
            dbServerStatus_label.AutoSize = true;
            dbServerStatus_label.Location = new Point(3, 7);
            dbServerStatus_label.Name = "dbServerStatus_label";
            dbServerStatus_label.Size = new Size(190, 15);
            dbServerStatus_label.TabIndex = 0;
            dbServerStatus_label.Text = "Database Server Connection Status";
            // 
            // dbServerConnStatus_bulb
            // 
            dbServerConnStatus_bulb.Anchor = AnchorStyles.Right;
            dbServerConnStatus_bulb.Location = new Point(432, 3);
            dbServerConnStatus_bulb.Name = "dbServerConnStatus_bulb";
            dbServerConnStatus_bulb.On = true;
            dbServerConnStatus_bulb.Size = new Size(23, 23);
            dbServerConnStatus_bulb.TabIndex = 1;
            dbServerConnStatus_bulb.Text = "ledBulb1";
            // 
            // digital
            // 
            digital.Controls.Add(digital_main_panel);
            digital.Location = new Point(4, 24);
            digital.Name = "digital";
            digital.Padding = new Padding(3);
            digital.Size = new Size(476, 533);
            digital.TabIndex = 1;
            digital.Text = "Digital I/O";
            digital.UseVisualStyleBackColor = true;
            // 
            // digital_main_panel
            // 
            digital_main_panel.ColumnCount = 2;
            digital_main_panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 29.361702F));
            digital_main_panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 70.6383F));
            digital_main_panel.Controls.Add(pinA_group, 0, 0);
            digital_main_panel.Controls.Add(portC_group, 1, 0);
            digital_main_panel.Dock = DockStyle.Fill;
            digital_main_panel.Location = new Point(3, 3);
            digital_main_panel.Margin = new Padding(0);
            digital_main_panel.Name = "digital_main_panel";
            digital_main_panel.RowCount = 2;
            digital_main_panel.RowStyles.Add(new RowStyle(SizeType.Percent, 50F));
            digital_main_panel.RowStyles.Add(new RowStyle(SizeType.Percent, 50F));
            digital_main_panel.Size = new Size(470, 527);
            digital_main_panel.TabIndex = 0;
            // 
            // pinA_group
            // 
            pinA_group.Controls.Add(pinA_panel);
            pinA_group.Dock = DockStyle.Fill;
            pinA_group.Location = new Point(3, 3);
            pinA_group.Name = "pinA_group";
            pinA_group.Size = new Size(132, 257);
            pinA_group.TabIndex = 0;
            pinA_group.TabStop = false;
            pinA_group.Text = "PINA";
            // 
            // pinA_panel
            // 
            pinA_panel.ColumnCount = 2;
            pinA_panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            pinA_panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            pinA_panel.Controls.Add(pa0_label, 1, 0);
            pinA_panel.Controls.Add(pa1_label, 1, 1);
            pinA_panel.Controls.Add(pa2_label, 1, 2);
            pinA_panel.Controls.Add(pa3_label, 1, 3);
            pinA_panel.Controls.Add(pa4_label, 1, 4);
            pinA_panel.Controls.Add(pa5_label, 1, 5);
            pinA_panel.Controls.Add(pa6_label, 1, 6);
            pinA_panel.Controls.Add(pa7_label, 1, 7);
            pinA_panel.Controls.Add(pa0_bulb, 0, 0);
            pinA_panel.Controls.Add(pa1_bulb, 0, 1);
            pinA_panel.Controls.Add(pa2_bulb, 0, 2);
            pinA_panel.Controls.Add(pa3_bulb, 0, 3);
            pinA_panel.Controls.Add(pa4_bulb, 0, 4);
            pinA_panel.Controls.Add(pa5_bulb, 0, 5);
            pinA_panel.Controls.Add(pa6_bulb, 0, 6);
            pinA_panel.Controls.Add(pa7_bulb, 0, 7);
            pinA_panel.Dock = DockStyle.Fill;
            pinA_panel.Location = new Point(3, 19);
            pinA_panel.Margin = new Padding(0);
            pinA_panel.Name = "pinA_panel";
            pinA_panel.RowCount = 8;
            pinA_panel.RowStyles.Add(new RowStyle(SizeType.Percent, 12.5F));
            pinA_panel.RowStyles.Add(new RowStyle(SizeType.Percent, 12.5F));
            pinA_panel.RowStyles.Add(new RowStyle(SizeType.Percent, 12.5F));
            pinA_panel.RowStyles.Add(new RowStyle(SizeType.Percent, 12.5F));
            pinA_panel.RowStyles.Add(new RowStyle(SizeType.Percent, 12.5F));
            pinA_panel.RowStyles.Add(new RowStyle(SizeType.Percent, 12.5F));
            pinA_panel.RowStyles.Add(new RowStyle(SizeType.Percent, 12.5F));
            pinA_panel.RowStyles.Add(new RowStyle(SizeType.Percent, 12.5F));
            pinA_panel.Size = new Size(126, 235);
            pinA_panel.TabIndex = 0;
            // 
            // pa0_label
            // 
            pa0_label.Anchor = AnchorStyles.Right;
            pa0_label.AutoSize = true;
            pa0_label.Location = new Point(96, 7);
            pa0_label.Name = "pa0_label";
            pa0_label.Size = new Size(27, 15);
            pa0_label.TabIndex = 1;
            pa0_label.Text = "PA0";
            // 
            // pa1_label
            // 
            pa1_label.Anchor = AnchorStyles.Right;
            pa1_label.AutoSize = true;
            pa1_label.Location = new Point(96, 36);
            pa1_label.Name = "pa1_label";
            pa1_label.Size = new Size(27, 15);
            pa1_label.TabIndex = 3;
            pa1_label.Text = "PA1";
            // 
            // pa2_label
            // 
            pa2_label.Anchor = AnchorStyles.Right;
            pa2_label.AutoSize = true;
            pa2_label.Location = new Point(96, 65);
            pa2_label.Name = "pa2_label";
            pa2_label.Size = new Size(27, 15);
            pa2_label.TabIndex = 5;
            pa2_label.Text = "PA2";
            // 
            // pa3_label
            // 
            pa3_label.Anchor = AnchorStyles.Right;
            pa3_label.AutoSize = true;
            pa3_label.Location = new Point(96, 94);
            pa3_label.Name = "pa3_label";
            pa3_label.Size = new Size(27, 15);
            pa3_label.TabIndex = 7;
            pa3_label.Text = "PA3";
            // 
            // pa4_label
            // 
            pa4_label.Anchor = AnchorStyles.Right;
            pa4_label.AutoSize = true;
            pa4_label.Location = new Point(96, 123);
            pa4_label.Name = "pa4_label";
            pa4_label.Size = new Size(27, 15);
            pa4_label.TabIndex = 9;
            pa4_label.Text = "PA4";
            // 
            // pa5_label
            // 
            pa5_label.Anchor = AnchorStyles.Right;
            pa5_label.AutoSize = true;
            pa5_label.Location = new Point(96, 152);
            pa5_label.Name = "pa5_label";
            pa5_label.Size = new Size(27, 15);
            pa5_label.TabIndex = 11;
            pa5_label.Text = "PA5";
            // 
            // pa6_label
            // 
            pa6_label.Anchor = AnchorStyles.Right;
            pa6_label.AutoSize = true;
            pa6_label.Location = new Point(96, 181);
            pa6_label.Name = "pa6_label";
            pa6_label.Size = new Size(27, 15);
            pa6_label.TabIndex = 13;
            pa6_label.Text = "PA6";
            // 
            // pa7_label
            // 
            pa7_label.Anchor = AnchorStyles.Right;
            pa7_label.AutoSize = true;
            pa7_label.Location = new Point(96, 211);
            pa7_label.Name = "pa7_label";
            pa7_label.Size = new Size(27, 15);
            pa7_label.TabIndex = 15;
            pa7_label.Text = "PA7";
            // 
            // pa0_bulb
            // 
            pa0_bulb.Anchor = AnchorStyles.Left;
            pa0_bulb.Location = new Point(3, 3);
            pa0_bulb.Name = "pa0_bulb";
            pa0_bulb.On = true;
            pa0_bulb.Size = new Size(23, 23);
            pa0_bulb.TabIndex = 0;
            pa0_bulb.Text = "ledBulb1";
            // 
            // pa1_bulb
            // 
            pa1_bulb.Anchor = AnchorStyles.Left;
            pa1_bulb.Location = new Point(3, 32);
            pa1_bulb.Name = "pa1_bulb";
            pa1_bulb.On = true;
            pa1_bulb.Size = new Size(23, 23);
            pa1_bulb.TabIndex = 2;
            pa1_bulb.Text = "ledBulb1";
            // 
            // pa2_bulb
            // 
            pa2_bulb.Anchor = AnchorStyles.Left;
            pa2_bulb.Location = new Point(3, 61);
            pa2_bulb.Name = "pa2_bulb";
            pa2_bulb.On = true;
            pa2_bulb.Size = new Size(23, 23);
            pa2_bulb.TabIndex = 4;
            pa2_bulb.Text = "ledBulb1";
            // 
            // pa3_bulb
            // 
            pa3_bulb.Anchor = AnchorStyles.Left;
            pa3_bulb.Location = new Point(3, 90);
            pa3_bulb.Name = "pa3_bulb";
            pa3_bulb.On = true;
            pa3_bulb.Size = new Size(23, 23);
            pa3_bulb.TabIndex = 6;
            pa3_bulb.Text = "ledBulb1";
            // 
            // pa4_bulb
            // 
            pa4_bulb.Anchor = AnchorStyles.Left;
            pa4_bulb.Location = new Point(3, 119);
            pa4_bulb.Name = "pa4_bulb";
            pa4_bulb.On = true;
            pa4_bulb.Size = new Size(23, 23);
            pa4_bulb.TabIndex = 8;
            pa4_bulb.Text = "ledBulb1";
            // 
            // pa5_bulb
            // 
            pa5_bulb.Anchor = AnchorStyles.Left;
            pa5_bulb.Location = new Point(3, 148);
            pa5_bulb.Name = "pa5_bulb";
            pa5_bulb.On = true;
            pa5_bulb.Size = new Size(23, 23);
            pa5_bulb.TabIndex = 10;
            pa5_bulb.Text = "ledBulb1";
            // 
            // pa6_bulb
            // 
            pa6_bulb.Anchor = AnchorStyles.Left;
            pa6_bulb.Location = new Point(3, 177);
            pa6_bulb.Name = "pa6_bulb";
            pa6_bulb.On = true;
            pa6_bulb.Size = new Size(23, 23);
            pa6_bulb.TabIndex = 12;
            pa6_bulb.Text = "ledBulb1";
            // 
            // pa7_bulb
            // 
            pa7_bulb.Anchor = AnchorStyles.Left;
            pa7_bulb.Location = new Point(3, 207);
            pa7_bulb.Name = "pa7_bulb";
            pa7_bulb.On = true;
            pa7_bulb.Size = new Size(23, 23);
            pa7_bulb.TabIndex = 14;
            pa7_bulb.Text = "ledBulb1";
            // 
            // portC_group
            // 
            portC_group.Controls.Add(portC_panel);
            portC_group.Dock = DockStyle.Fill;
            portC_group.Location = new Point(141, 3);
            portC_group.Name = "portC_group";
            portC_group.Size = new Size(326, 257);
            portC_group.TabIndex = 1;
            portC_group.TabStop = false;
            portC_group.Text = "PORTC";
            // 
            // portC_panel
            // 
            portC_panel.ColumnCount = 3;
            portC_panel.ColumnStyles.Add(new ColumnStyle());
            portC_panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            portC_panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            portC_panel.Controls.Add(portCCheckbox_panel, 0, 0);
            portC_panel.Controls.Add(sevenSegment1, 1, 0);
            portC_panel.Controls.Add(sevenSegment2, 2, 0);
            portC_panel.Dock = DockStyle.Fill;
            portC_panel.Location = new Point(3, 19);
            portC_panel.Margin = new Padding(0);
            portC_panel.Name = "portC_panel";
            portC_panel.RowCount = 1;
            portC_panel.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            portC_panel.Size = new Size(320, 235);
            portC_panel.TabIndex = 0;
            // 
            // portCCheckbox_panel
            // 
            portCCheckbox_panel.AutoSize = true;
            portCCheckbox_panel.AutoSizeMode = AutoSizeMode.GrowAndShrink;
            portCCheckbox_panel.ColumnCount = 1;
            portCCheckbox_panel.ColumnStyles.Add(new ColumnStyle());
            portCCheckbox_panel.Controls.Add(pc0, 0, 0);
            portCCheckbox_panel.Controls.Add(pc1, 0, 1);
            portCCheckbox_panel.Controls.Add(pc2, 0, 2);
            portCCheckbox_panel.Controls.Add(pc3, 0, 3);
            portCCheckbox_panel.Controls.Add(pc4, 0, 4);
            portCCheckbox_panel.Controls.Add(pc5, 0, 5);
            portCCheckbox_panel.Controls.Add(pc6, 0, 6);
            portCCheckbox_panel.Controls.Add(pc7, 0, 7);
            portCCheckbox_panel.Dock = DockStyle.Fill;
            portCCheckbox_panel.Location = new Point(0, 0);
            portCCheckbox_panel.Margin = new Padding(0);
            portCCheckbox_panel.Name = "portCCheckbox_panel";
            portCCheckbox_panel.RowCount = 8;
            portCCheckbox_panel.RowStyles.Add(new RowStyle(SizeType.Percent, 12.5F));
            portCCheckbox_panel.RowStyles.Add(new RowStyle(SizeType.Percent, 12.5F));
            portCCheckbox_panel.RowStyles.Add(new RowStyle(SizeType.Percent, 12.5F));
            portCCheckbox_panel.RowStyles.Add(new RowStyle(SizeType.Percent, 12.5F));
            portCCheckbox_panel.RowStyles.Add(new RowStyle(SizeType.Percent, 12.5F));
            portCCheckbox_panel.RowStyles.Add(new RowStyle(SizeType.Percent, 12.5F));
            portCCheckbox_panel.RowStyles.Add(new RowStyle(SizeType.Percent, 12.5F));
            portCCheckbox_panel.RowStyles.Add(new RowStyle(SizeType.Percent, 12.5F));
            portCCheckbox_panel.Size = new Size(53, 235);
            portCCheckbox_panel.TabIndex = 0;
            // 
            // pc0
            // 
            pc0.Anchor = AnchorStyles.Left;
            pc0.AutoSize = true;
            pc0.Location = new Point(3, 5);
            pc0.Name = "pc0";
            pc0.Size = new Size(47, 19);
            pc0.TabIndex = 0;
            pc0.Text = "PC0";
            pc0.UseVisualStyleBackColor = true;
            // 
            // pc1
            // 
            pc1.Anchor = AnchorStyles.Left;
            pc1.AutoSize = true;
            pc1.Location = new Point(3, 34);
            pc1.Name = "pc1";
            pc1.Size = new Size(47, 19);
            pc1.TabIndex = 1;
            pc1.Text = "PC1";
            pc1.UseVisualStyleBackColor = true;
            // 
            // pc2
            // 
            pc2.Anchor = AnchorStyles.Left;
            pc2.AutoSize = true;
            pc2.Location = new Point(3, 63);
            pc2.Name = "pc2";
            pc2.Size = new Size(47, 19);
            pc2.TabIndex = 2;
            pc2.Text = "PC2";
            pc2.UseVisualStyleBackColor = true;
            // 
            // pc3
            // 
            pc3.Anchor = AnchorStyles.Left;
            pc3.AutoSize = true;
            pc3.Location = new Point(3, 92);
            pc3.Name = "pc3";
            pc3.Size = new Size(47, 19);
            pc3.TabIndex = 3;
            pc3.Text = "PC3";
            pc3.UseVisualStyleBackColor = true;
            // 
            // pc4
            // 
            pc4.Anchor = AnchorStyles.Left;
            pc4.AutoSize = true;
            pc4.Location = new Point(3, 121);
            pc4.Name = "pc4";
            pc4.Size = new Size(47, 19);
            pc4.TabIndex = 4;
            pc4.Text = "PC4";
            pc4.UseVisualStyleBackColor = true;
            // 
            // pc5
            // 
            pc5.Anchor = AnchorStyles.Left;
            pc5.AutoSize = true;
            pc5.Location = new Point(3, 150);
            pc5.Name = "pc5";
            pc5.Size = new Size(47, 19);
            pc5.TabIndex = 5;
            pc5.Text = "PC5";
            pc5.UseVisualStyleBackColor = true;
            // 
            // pc6
            // 
            pc6.Anchor = AnchorStyles.Left;
            pc6.AutoSize = true;
            pc6.Location = new Point(3, 179);
            pc6.Name = "pc6";
            pc6.Size = new Size(47, 19);
            pc6.TabIndex = 6;
            pc6.Text = "PC6";
            pc6.UseVisualStyleBackColor = true;
            // 
            // pc7
            // 
            pc7.Anchor = AnchorStyles.Left;
            pc7.AutoSize = true;
            pc7.Location = new Point(3, 209);
            pc7.Name = "pc7";
            pc7.Size = new Size(47, 19);
            pc7.TabIndex = 7;
            pc7.Text = "PC7";
            pc7.UseVisualStyleBackColor = true;
            // 
            // sevenSegment1
            // 
            sevenSegment1.ColorBackground = Color.DarkGray;
            sevenSegment1.ColorDark = Color.DimGray;
            sevenSegment1.ColorLight = Color.Red;
            sevenSegment1.CustomPattern = 0;
            sevenSegment1.DecimalOn = false;
            sevenSegment1.DecimalShow = true;
            sevenSegment1.Dock = DockStyle.Fill;
            sevenSegment1.ElementWidth = 10;
            sevenSegment1.ItalicFactor = 0F;
            sevenSegment1.Location = new Point(56, 3);
            sevenSegment1.Name = "sevenSegment1";
            sevenSegment1.Padding = new Padding(4);
            sevenSegment1.Size = new Size(127, 229);
            sevenSegment1.TabIndex = 1;
            sevenSegment1.TabStop = false;
            sevenSegment1.Value = null;
            // 
            // sevenSegment2
            // 
            sevenSegment2.ColorBackground = Color.DarkGray;
            sevenSegment2.ColorDark = Color.DimGray;
            sevenSegment2.ColorLight = Color.Red;
            sevenSegment2.CustomPattern = 0;
            sevenSegment2.DecimalOn = false;
            sevenSegment2.DecimalShow = true;
            sevenSegment2.Dock = DockStyle.Fill;
            sevenSegment2.ElementWidth = 10;
            sevenSegment2.ItalicFactor = 0F;
            sevenSegment2.Location = new Point(189, 3);
            sevenSegment2.Name = "sevenSegment2";
            sevenSegment2.Padding = new Padding(4);
            sevenSegment2.Size = new Size(128, 229);
            sevenSegment2.TabIndex = 2;
            sevenSegment2.TabStop = false;
            sevenSegment2.Value = null;
            // 
            // lights
            // 
            lights.Controls.Add(lights_main_panel);
            lights.Location = new Point(4, 24);
            lights.Name = "lights";
            lights.Padding = new Padding(3);
            lights.Size = new Size(476, 533);
            lights.TabIndex = 2;
            lights.Text = "Ports-Lights";
            lights.UseVisualStyleBackColor = true;
            // 
            // lights_main_panel
            // 
            lights_main_panel.ColumnCount = 1;
            lights_main_panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            lights_main_panel.Controls.Add(lights_ports_group, 0, 0);
            lights_main_panel.Controls.Add(light_lights_group, 0, 1);
            lights_main_panel.Dock = DockStyle.Fill;
            lights_main_panel.Location = new Point(3, 3);
            lights_main_panel.Margin = new Padding(0);
            lights_main_panel.Name = "lights_main_panel";
            lights_main_panel.RowCount = 2;
            lights_main_panel.RowStyles.Add(new RowStyle(SizeType.Percent, 52.6315842F));
            lights_main_panel.RowStyles.Add(new RowStyle(SizeType.Percent, 47.36842F));
            lights_main_panel.Size = new Size(470, 527);
            lights_main_panel.TabIndex = 0;
            // 
            // lights_ports_group
            // 
            lights_ports_group.Controls.Add(potPorts_panel);
            lights_ports_group.Dock = DockStyle.Fill;
            lights_ports_group.Location = new Point(3, 3);
            lights_ports_group.Name = "lights_ports_group";
            lights_ports_group.Size = new Size(464, 271);
            lights_ports_group.TabIndex = 0;
            lights_ports_group.TabStop = false;
            lights_ports_group.Text = "Ports";
            // 
            // potPorts_panel
            // 
            potPorts_panel.ColumnCount = 2;
            potPorts_panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            potPorts_panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            potPorts_panel.Controls.Add(pot1_label, 0, 0);
            potPorts_panel.Controls.Add(pot2_label, 1, 0);
            potPorts_panel.Controls.Add(pot1, 0, 1);
            potPorts_panel.Controls.Add(pot2, 1, 1);
            potPorts_panel.Dock = DockStyle.Fill;
            potPorts_panel.Location = new Point(3, 19);
            potPorts_panel.Margin = new Padding(0);
            potPorts_panel.Name = "potPorts_panel";
            potPorts_panel.RowCount = 2;
            potPorts_panel.RowStyles.Add(new RowStyle());
            potPorts_panel.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            potPorts_panel.Size = new Size(458, 249);
            potPorts_panel.TabIndex = 0;
            // 
            // pot1_label
            // 
            pot1_label.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            pot1_label.AutoSize = true;
            pot1_label.Font = new Font("Segoe UI", 11.25F, FontStyle.Bold, GraphicsUnit.Point, 0);
            pot1_label.Location = new Point(3, 0);
            pot1_label.Name = "pot1_label";
            pot1_label.Size = new Size(223, 20);
            pot1_label.TabIndex = 0;
            pot1_label.Text = "Pot1 Voltage";
            pot1_label.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // pot2_label
            // 
            pot2_label.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            pot2_label.AutoSize = true;
            pot2_label.Font = new Font("Segoe UI", 11.25F, FontStyle.Bold, GraphicsUnit.Point, 0);
            pot2_label.Location = new Point(232, 0);
            pot2_label.Name = "pot2_label";
            pot2_label.Size = new Size(223, 20);
            pot2_label.TabIndex = 1;
            pot2_label.Text = "Pot2 Voltage";
            pot2_label.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // pot1
            // 
            pot1.AutoSizeMode = AutoSizeMode.GrowAndShrink;
            pot1.BackColor = Color.Transparent;
            pot1.DialColor = Color.Lavender;
            pot1.DialText = null;
            pot1.Dock = DockStyle.Fill;
            pot1.Glossiness = 11.363636F;
            pot1.Location = new Point(4, 23);
            pot1.Margin = new Padding(4, 3, 4, 3);
            pot1.MaxValue = 0F;
            pot1.MinValue = 0F;
            pot1.Name = "pot1";
            pot1.RecommendedValue = 0F;
            pot1.Size = new Size(221, 221);
            pot1.TabIndex = 2;
            pot1.ThresholdPercent = 0F;
            pot1.Value = 0F;
            // 
            // pot2
            // 
            pot2.BackColor = Color.Transparent;
            pot2.DialColor = Color.Lavender;
            pot2.DialText = null;
            pot2.Dock = DockStyle.Fill;
            pot2.Glossiness = 11.363636F;
            pot2.Location = new Point(233, 23);
            pot2.Margin = new Padding(4, 3, 4, 3);
            pot2.MaxValue = 0F;
            pot2.MinValue = 0F;
            pot2.Name = "pot2";
            pot2.RecommendedValue = 0F;
            pot2.Size = new Size(221, 221);
            pot2.TabIndex = 3;
            pot2.ThresholdPercent = 0F;
            pot2.Value = 0F;
            // 
            // light_lights_group
            // 
            light_lights_group.Controls.Add(tableLayoutPanel2);
            light_lights_group.Dock = DockStyle.Fill;
            light_lights_group.Location = new Point(3, 280);
            light_lights_group.Name = "light_lights_group";
            light_lights_group.Size = new Size(464, 244);
            light_lights_group.TabIndex = 1;
            light_lights_group.TabStop = false;
            light_lights_group.Text = "Light";
            // 
            // tableLayoutPanel2
            // 
            tableLayoutPanel2.ColumnCount = 2;
            tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 20F));
            tableLayoutPanel2.Controls.Add(lightPot, 1, 0);
            tableLayoutPanel2.Controls.Add(lightPot_panel, 0, 0);
            tableLayoutPanel2.Dock = DockStyle.Fill;
            tableLayoutPanel2.Location = new Point(3, 19);
            tableLayoutPanel2.Margin = new Padding(0);
            tableLayoutPanel2.Name = "tableLayoutPanel2";
            tableLayoutPanel2.RowCount = 1;
            tableLayoutPanel2.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tableLayoutPanel2.Size = new Size(458, 222);
            tableLayoutPanel2.TabIndex = 0;
            // 
            // lightPot
            // 
            lightPot.BackColor = Color.Transparent;
            lightPot.DialColor = Color.Lavender;
            lightPot.DialText = null;
            lightPot.Dock = DockStyle.Fill;
            lightPot.Glossiness = 11.363636F;
            lightPot.Location = new Point(233, 3);
            lightPot.Margin = new Padding(4, 3, 4, 3);
            lightPot.MaxValue = 0F;
            lightPot.MinValue = 0F;
            lightPot.Name = "lightPot";
            lightPot.RecommendedValue = 0F;
            lightPot.Size = new Size(221, 221);
            lightPot.TabIndex = 0;
            lightPot.ThresholdPercent = 0F;
            lightPot.Value = 0F;
            // 
            // lightPot_panel
            // 
            lightPot_panel.ColumnCount = 2;
            lightPot_panel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 40F));
            lightPot_panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            lightPot_panel.Controls.Add(lightPot_scroll, 0, 0);
            lightPot_panel.Controls.Add(lightPot_text, 1, 0);
            lightPot_panel.Dock = DockStyle.Fill;
            lightPot_panel.Location = new Point(0, 0);
            lightPot_panel.Margin = new Padding(0);
            lightPot_panel.Name = "lightPot_panel";
            lightPot_panel.RowCount = 1;
            lightPot_panel.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            lightPot_panel.Size = new Size(229, 222);
            lightPot_panel.TabIndex = 1;
            // 
            // lightPot_scroll
            // 
            lightPot_scroll.Dock = DockStyle.Fill;
            lightPot_scroll.Location = new Point(3, 3);
            lightPot_scroll.Margin = new Padding(3);
            lightPot_scroll.Name = "lightPot_scroll";
            lightPot_scroll.Size = new Size(34, 216);
            lightPot_scroll.TabIndex = 0;
            // 
            // lightPot_text
            // 
            lightPot_text.Anchor = AnchorStyles.Left;
            lightPot_text.Location = new Point(43, 99);
            lightPot_text.Name = "lightPot_text";
            lightPot_text.Size = new Size(100, 23);
            lightPot_text.TabIndex = 1;
            // 
            // temp
            // 
            temp.Controls.Add(temp_main_panel);
            temp.Location = new Point(4, 24);
            temp.Name = "temp";
            temp.Padding = new Padding(3);
            temp.Size = new Size(476, 533);
            temp.TabIndex = 3;
            temp.Text = "Temp Control";
            temp.UseVisualStyleBackColor = true;
            // 
            // temp_main_panel
            // 
            temp_main_panel.ColumnCount = 1;
            temp_main_panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            temp_main_panel.Controls.Add(control_group, 0, 0);
            temp_main_panel.Controls.Add(cloudDataLogging_group, 0, 1);
            temp_main_panel.Dock = DockStyle.Fill;
            temp_main_panel.Location = new Point(3, 3);
            temp_main_panel.Margin = new Padding(0);
            temp_main_panel.Name = "temp_main_panel";
            temp_main_panel.RowCount = 2;
            temp_main_panel.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            temp_main_panel.RowStyles.Add(new RowStyle());
            temp_main_panel.Size = new Size(470, 527);
            temp_main_panel.TabIndex = 0;
            // 
            // control_group
            // 
            control_group.Controls.Add(control_panel);
            control_group.Dock = DockStyle.Fill;
            control_group.Location = new Point(0, 0);
            control_group.Margin = new Padding(0, 0, 0, 3);
            control_group.Name = "control_group";
            control_group.Size = new Size(470, 401);
            control_group.TabIndex = 1;
            control_group.TabStop = false;
            control_group.Text = "Control";
            // 
            // control_panel
            // 
            control_panel.ColumnCount = 2;
            control_panel.ColumnStyles.Add(new ColumnStyle());
            control_panel.ColumnStyles.Add(new ColumnStyle());
            control_panel.Controls.Add(controlSettings_panel, 0, 0);
            control_panel.Dock = DockStyle.Fill;
            control_panel.Location = new Point(3, 19);
            control_panel.Margin = new Padding(0);
            control_panel.Name = "control_panel";
            control_panel.RowCount = 1;
            control_panel.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            control_panel.Size = new Size(464, 379);
            control_panel.TabIndex = 0;
            // 
            // controlSettings_panel
            // 
            controlSettings_panel.AutoSize = true;
            controlSettings_panel.AutoSizeMode = AutoSizeMode.GrowAndShrink;
            controlSettings_panel.ColumnCount = 1;
            controlSettings_panel.ColumnStyles.Add(new ColumnStyle());
            controlSettings_panel.Controls.Add(setPointTemp_panel, 0, 0);
            controlSettings_panel.Controls.Add(pITuning_panel, 0, 1);
            controlSettings_panel.Controls.Add(tempMiscValues_panel, 0, 2);
            controlSettings_panel.Dock = DockStyle.Fill;
            controlSettings_panel.Location = new Point(0, 3);
            controlSettings_panel.Margin = new Padding(0, 3, 3, 3);
            controlSettings_panel.Name = "controlSettings_panel";
            controlSettings_panel.RowCount = 3;
            controlSettings_panel.RowStyles.Add(new RowStyle());
            controlSettings_panel.RowStyles.Add(new RowStyle());
            controlSettings_panel.RowStyles.Add(new RowStyle());
            controlSettings_panel.Size = new Size(109, 373);
            controlSettings_panel.TabIndex = 0;
            // 
            // setPointTemp_panel
            // 
            setPointTemp_panel.AutoSize = true;
            setPointTemp_panel.ColumnCount = 1;
            setPointTemp_panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            setPointTemp_panel.Controls.Add(setPointTemp_label, 0, 0);
            setPointTemp_panel.Controls.Add(setPointTemp_upDown, 0, 1);
            setPointTemp_panel.Dock = DockStyle.Top;
            setPointTemp_panel.Location = new Point(0, 0);
            setPointTemp_panel.Margin = new Padding(0);
            setPointTemp_panel.Name = "setPointTemp_panel";
            setPointTemp_panel.RowCount = 2;
            setPointTemp_panel.RowStyles.Add(new RowStyle());
            setPointTemp_panel.RowStyles.Add(new RowStyle());
            setPointTemp_panel.RowStyles.Add(new RowStyle(SizeType.Absolute, 20F));
            setPointTemp_panel.Size = new Size(109, 47);
            setPointTemp_panel.TabIndex = 0;
            // 
            // setPointTemp_label
            // 
            setPointTemp_label.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            setPointTemp_label.AutoSize = true;
            setPointTemp_label.Location = new Point(3, 3);
            setPointTemp_label.Margin = new Padding(3, 3, 3, 0);
            setPointTemp_label.Name = "setPointTemp_label";
            setPointTemp_label.Size = new Size(103, 15);
            setPointTemp_label.TabIndex = 0;
            setPointTemp_label.Text = "Setpoint Temp [C]";
            setPointTemp_label.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // setPointTemp_upDown
            // 
            setPointTemp_upDown.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            setPointTemp_upDown.AutoSize = true;
            setPointTemp_upDown.Location = new Point(3, 21);
            setPointTemp_upDown.Name = "setPointTemp_upDown";
            setPointTemp_upDown.Size = new Size(103, 23);
            setPointTemp_upDown.TabIndex = 1;
            // 
            // pITuning_panel
            // 
            pITuning_panel.AutoSize = true;
            pITuning_panel.AutoSizeMode = AutoSizeMode.GrowAndShrink;
            pITuning_panel.ColumnCount = 1;
            pITuning_panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            pITuning_panel.Controls.Add(pITuning_label, 0, 0);
            pITuning_panel.Controls.Add(pITuningKValues_panel, 0, 1);
            pITuning_panel.Dock = DockStyle.Top;
            pITuning_panel.Location = new Point(0, 47);
            pITuning_panel.Margin = new Padding(0);
            pITuning_panel.Name = "pITuning_panel";
            pITuning_panel.RowCount = 2;
            pITuning_panel.RowStyles.Add(new RowStyle());
            pITuning_panel.RowStyles.Add(new RowStyle());
            pITuning_panel.Size = new Size(109, 76);
            pITuning_panel.TabIndex = 1;
            // 
            // pITuning_label
            // 
            pITuning_label.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            pITuning_label.AutoSize = true;
            pITuning_label.Location = new Point(3, 3);
            pITuning_label.Margin = new Padding(3, 3, 3, 0);
            pITuning_label.Name = "pITuning_label";
            pITuning_label.Size = new Size(103, 15);
            pITuning_label.TabIndex = 0;
            pITuning_label.Text = "PI Tuning";
            pITuning_label.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // pITuningKValues_panel
            // 
            pITuningKValues_panel.AutoSize = true;
            pITuningKValues_panel.ColumnCount = 2;
            pITuningKValues_panel.ColumnStyles.Add(new ColumnStyle());
            pITuningKValues_panel.ColumnStyles.Add(new ColumnStyle());
            pITuningKValues_panel.Controls.Add(pITuningKP_label, 0, 0);
            pITuningKValues_panel.Controls.Add(pITuningKI_label, 0, 1);
            pITuningKValues_panel.Controls.Add(pITuningKP_upDown, 1, 0);
            pITuningKValues_panel.Controls.Add(pITuningKI_upDown, 1, 1);
            pITuningKValues_panel.Dock = DockStyle.Top;
            pITuningKValues_panel.Location = new Point(0, 18);
            pITuningKValues_panel.Margin = new Padding(0);
            pITuningKValues_panel.Name = "pITuningKValues_panel";
            pITuningKValues_panel.RowCount = 2;
            pITuningKValues_panel.RowStyles.Add(new RowStyle());
            pITuningKValues_panel.RowStyles.Add(new RowStyle());
            pITuningKValues_panel.Size = new Size(109, 58);
            pITuningKValues_panel.TabIndex = 1;
            // 
            // pITuningKP_label
            // 
            pITuningKP_label.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            pITuningKP_label.AutoSize = true;
            pITuningKP_label.Location = new Point(3, 7);
            pITuningKP_label.Margin = new Padding(3);
            pITuningKP_label.Name = "pITuningKP_label";
            pITuningKP_label.Size = new Size(21, 15);
            pITuningKP_label.TabIndex = 0;
            pITuningKP_label.Text = "Kp";
            pITuningKP_label.TextAlign = ContentAlignment.MiddleLeft;
            // 
            // pITuningKI_label
            // 
            pITuningKI_label.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            pITuningKI_label.AutoSize = true;
            pITuningKI_label.Location = new Point(3, 36);
            pITuningKI_label.Margin = new Padding(3);
            pITuningKI_label.Name = "pITuningKI_label";
            pITuningKI_label.Size = new Size(21, 15);
            pITuningKI_label.TabIndex = 1;
            pITuningKI_label.Text = "Ki";
            pITuningKI_label.TextAlign = ContentAlignment.MiddleLeft;
            // 
            // pITuningKP_upDown
            // 
            pITuningKP_upDown.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            pITuningKP_upDown.AutoSize = true;
            pITuningKP_upDown.Location = new Point(30, 3);
            pITuningKP_upDown.Name = "pITuningKP_upDown";
            pITuningKP_upDown.Size = new Size(76, 23);
            pITuningKP_upDown.TabIndex = 2;
            // 
            // pITuningKI_upDown
            // 
            pITuningKI_upDown.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            pITuningKI_upDown.AutoSize = true;
            pITuningKI_upDown.Location = new Point(30, 32);
            pITuningKI_upDown.Name = "pITuningKI_upDown";
            pITuningKI_upDown.Size = new Size(76, 23);
            pITuningKI_upDown.TabIndex = 3;
            // 
            // tempMiscValues_panel
            // 
            tempMiscValues_panel.AutoSize = true;
            tempMiscValues_panel.AutoSizeMode = AutoSizeMode.GrowAndShrink;
            tempMiscValues_panel.ColumnCount = 1;
            tempMiscValues_panel.ColumnStyles.Add(new ColumnStyle());
            tempMiscValues_panel.Controls.Add(actualTemp_label, 0, 0);
            tempMiscValues_panel.Controls.Add(motorSpeed_label, 0, 2);
            tempMiscValues_panel.Controls.Add(actualTemp_text, 0, 1);
            tempMiscValues_panel.Controls.Add(motorSpeed_text, 0, 3);
            tempMiscValues_panel.Dock = DockStyle.Top;
            tempMiscValues_panel.Location = new Point(0, 126);
            tempMiscValues_panel.Margin = new Padding(0, 3, 0, 0);
            tempMiscValues_panel.Name = "tempMiscValues_panel";
            tempMiscValues_panel.RowCount = 4;
            tempMiscValues_panel.RowStyles.Add(new RowStyle());
            tempMiscValues_panel.RowStyles.Add(new RowStyle());
            tempMiscValues_panel.RowStyles.Add(new RowStyle());
            tempMiscValues_panel.RowStyles.Add(new RowStyle());
            tempMiscValues_panel.Size = new Size(109, 100);
            tempMiscValues_panel.TabIndex = 2;
            // 
            // actualTemp_label
            // 
            actualTemp_label.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            actualTemp_label.AutoSize = true;
            actualTemp_label.Location = new Point(3, 3);
            actualTemp_label.Margin = new Padding(3);
            actualTemp_label.Name = "actualTemp_label";
            actualTemp_label.Size = new Size(103, 15);
            actualTemp_label.TabIndex = 0;
            actualTemp_label.Text = "Actual Temp [C]";
            actualTemp_label.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // motorSpeed_label
            // 
            motorSpeed_label.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            motorSpeed_label.AutoSize = true;
            motorSpeed_label.Location = new Point(3, 53);
            motorSpeed_label.Margin = new Padding(3);
            motorSpeed_label.Name = "motorSpeed_label";
            motorSpeed_label.Size = new Size(103, 15);
            motorSpeed_label.TabIndex = 1;
            motorSpeed_label.Text = "Motor Speed [%]";
            motorSpeed_label.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // actualTemp_text
            // 
            actualTemp_text.Dock = DockStyle.Fill;
            actualTemp_text.Location = new Point(3, 24);
            actualTemp_text.Name = "actualTemp_text";
            actualTemp_text.Size = new Size(103, 23);
            actualTemp_text.TabIndex = 2;
            // 
            // motorSpeed_text
            // 
            motorSpeed_text.Dock = DockStyle.Fill;
            motorSpeed_text.Location = new Point(3, 74);
            motorSpeed_text.Name = "motorSpeed_text";
            motorSpeed_text.Size = new Size(103, 23);
            motorSpeed_text.TabIndex = 3;
            // 
            // cloudDataLogging_group
            // 
            cloudDataLogging_group.AutoSize = true;
            cloudDataLogging_group.Controls.Add(cloudDataLogging_panel);
            cloudDataLogging_group.Dock = DockStyle.Top;
            cloudDataLogging_group.Location = new Point(0, 407);
            cloudDataLogging_group.Margin = new Padding(0, 3, 0, 0);
            cloudDataLogging_group.Name = "cloudDataLogging_group";
            cloudDataLogging_group.Padding = new Padding(0);
            cloudDataLogging_group.Size = new Size(470, 120);
            cloudDataLogging_group.TabIndex = 0;
            cloudDataLogging_group.TabStop = false;
            cloudDataLogging_group.Text = "Cloud Data Logging";
            // 
            // cloudDataLogging_panel
            // 
            cloudDataLogging_panel.AutoSize = true;
            cloudDataLogging_panel.ColumnCount = 2;
            cloudDataLogging_panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            cloudDataLogging_panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            cloudDataLogging_panel.Controls.Add(manualDataLogging_group, 0, 0);
            cloudDataLogging_panel.Controls.Add(autoDataLogging_group, 1, 0);
            cloudDataLogging_panel.Dock = DockStyle.Top;
            cloudDataLogging_panel.Location = new Point(0, 16);
            cloudDataLogging_panel.Margin = new Padding(0);
            cloudDataLogging_panel.Name = "cloudDataLogging_panel";
            cloudDataLogging_panel.RowCount = 1;
            cloudDataLogging_panel.RowStyles.Add(new RowStyle());
            cloudDataLogging_panel.Size = new Size(470, 104);
            cloudDataLogging_panel.TabIndex = 0;
            // 
            // manualDataLogging_group
            // 
            manualDataLogging_group.AutoSize = true;
            manualDataLogging_group.Controls.Add(manualDataLogging_panel);
            manualDataLogging_group.Dock = DockStyle.Top;
            manualDataLogging_group.Location = new Point(6, 3);
            manualDataLogging_group.Margin = new Padding(6, 3, 3, 6);
            manualDataLogging_group.Name = "manualDataLogging_group";
            manualDataLogging_group.Size = new Size(226, 80);
            manualDataLogging_group.TabIndex = 0;
            manualDataLogging_group.TabStop = false;
            manualDataLogging_group.Text = "Manual Data Logging";
            // 
            // manualDataLogging_panel
            // 
            manualDataLogging_panel.AutoSize = true;
            manualDataLogging_panel.ColumnCount = 1;
            manualDataLogging_panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            manualDataLogging_panel.Controls.Add(manualDataLogging_text, 0, 0);
            manualDataLogging_panel.Controls.Add(manualDataLoggingInsert_button, 0, 1);
            manualDataLogging_panel.Dock = DockStyle.Top;
            manualDataLogging_panel.Location = new Point(3, 19);
            manualDataLogging_panel.Margin = new Padding(0);
            manualDataLogging_panel.Name = "manualDataLogging_panel";
            manualDataLogging_panel.RowCount = 2;
            manualDataLogging_panel.RowStyles.Add(new RowStyle());
            manualDataLogging_panel.RowStyles.Add(new RowStyle());
            manualDataLogging_panel.Size = new Size(220, 58);
            manualDataLogging_panel.TabIndex = 0;
            // 
            // manualDataLogging_text
            // 
            manualDataLogging_text.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            manualDataLogging_text.Location = new Point(3, 3);
            manualDataLogging_text.Name = "manualDataLogging_text";
            manualDataLogging_text.Size = new Size(214, 23);
            manualDataLogging_text.TabIndex = 0;
            // 
            // manualDataLoggingInsert_button
            // 
            manualDataLoggingInsert_button.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            manualDataLoggingInsert_button.Location = new Point(3, 32);
            manualDataLoggingInsert_button.Name = "manualDataLoggingInsert_button";
            manualDataLoggingInsert_button.Size = new Size(214, 23);
            manualDataLoggingInsert_button.TabIndex = 1;
            manualDataLoggingInsert_button.Text = "Insert Data to Table";
            manualDataLoggingInsert_button.UseVisualStyleBackColor = true;
            // 
            // autoDataLogging_group
            // 
            autoDataLogging_group.AutoSize = true;
            autoDataLogging_group.Controls.Add(autoDataLogging_panel);
            autoDataLogging_group.Dock = DockStyle.Top;
            autoDataLogging_group.Location = new Point(238, 3);
            autoDataLogging_group.Margin = new Padding(3, 3, 6, 6);
            autoDataLogging_group.Name = "autoDataLogging_group";
            autoDataLogging_group.Size = new Size(226, 95);
            autoDataLogging_group.TabIndex = 1;
            autoDataLogging_group.TabStop = false;
            autoDataLogging_group.Text = "Auto Data Logging";
            // 
            // autoDataLogging_panel
            // 
            autoDataLogging_panel.AutoSize = true;
            autoDataLogging_panel.ColumnCount = 1;
            autoDataLogging_panel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            autoDataLogging_panel.Controls.Add(autoDataLoggingEnable_button, 0, 0);
            autoDataLogging_panel.Controls.Add(autoDataLoggingDisable_button, 0, 2);
            autoDataLogging_panel.Controls.Add(autoDataLoggingStatus_label, 0, 1);
            autoDataLogging_panel.Dock = DockStyle.Top;
            autoDataLogging_panel.Location = new Point(3, 19);
            autoDataLogging_panel.Margin = new Padding(0);
            autoDataLogging_panel.Name = "autoDataLogging_panel";
            autoDataLogging_panel.RowCount = 3;
            autoDataLogging_panel.RowStyles.Add(new RowStyle());
            autoDataLogging_panel.RowStyles.Add(new RowStyle());
            autoDataLogging_panel.RowStyles.Add(new RowStyle());
            autoDataLogging_panel.Size = new Size(220, 73);
            autoDataLogging_panel.TabIndex = 0;
            // 
            // autoDataLoggingEnable_button
            // 
            autoDataLoggingEnable_button.Anchor = AnchorStyles.Left | AnchorStyles.Right;
            autoDataLoggingEnable_button.Location = new Point(3, 3);
            autoDataLoggingEnable_button.Name = "autoDataLoggingEnable_button";
            autoDataLoggingEnable_button.Size = new Size(214, 23);
            autoDataLoggingEnable_button.TabIndex = 0;
            autoDataLoggingEnable_button.Text = "button1";
            autoDataLoggingEnable_button.UseVisualStyleBackColor = true;
            // 
            // autoDataLoggingDisable_button
            // 
            autoDataLoggingDisable_button.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            autoDataLoggingDisable_button.Location = new Point(3, 47);
            autoDataLoggingDisable_button.Name = "autoDataLoggingDisable_button";
            autoDataLoggingDisable_button.Size = new Size(214, 23);
            autoDataLoggingDisable_button.TabIndex = 1;
            autoDataLoggingDisable_button.Text = "button2";
            autoDataLoggingDisable_button.UseVisualStyleBackColor = true;
            // 
            // autoDataLoggingStatus_label
            // 
            autoDataLoggingStatus_label.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            autoDataLoggingStatus_label.AutoSize = true;
            autoDataLoggingStatus_label.Location = new Point(3, 29);
            autoDataLoggingStatus_label.Name = "autoDataLoggingStatus_label";
            autoDataLoggingStatus_label.Size = new Size(214, 15);
            autoDataLoggingStatus_label.TabIndex = 2;
            autoDataLoggingStatus_label.Text = "status";
            // 
            // MainForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            AutoSize = true;
            AutoSizeMode = AutoSizeMode.GrowAndShrink;
            ClientSize = new Size(484, 561);
            Controls.Add(tabs);
            MinimumSize = new Size(500, 600);
            Name = "MainForm";
            Text = "AUT Application Board Control";
            tabs.ResumeLayout(false);
            setup.ResumeLayout(false);
            setup.PerformLayout();
            setup_main_panel.ResumeLayout(false);
            setup_main_panel.PerformLayout();
            serialPortConnection_group.ResumeLayout(false);
            serialPortConnection_group.PerformLayout();
            serialPortConnection_panel.ResumeLayout(false);
            serialPortConnection_panel.PerformLayout();
            serialPortStatus_panel.ResumeLayout(false);
            serialPortStatus_panel.PerformLayout();
            serialPortConnectionSettings_panel.ResumeLayout(false);
            serialPortConnectionSettings_panel.PerformLayout();
            serialPortConnectionButtons_panel.ResumeLayout(false);
            databaseServerConnection_group.ResumeLayout(false);
            databaseServerConnection_group.PerformLayout();
            dbServerConn_panel.ResumeLayout(false);
            dbServerConn_panel.PerformLayout();
            dbServerConnSettings_panel.ResumeLayout(false);
            dbServerConnSettings_panel.PerformLayout();
            dbServerConnectionButtons_panel.ResumeLayout(false);
            databaseServerStatus_panel.ResumeLayout(false);
            databaseServerStatus_panel.PerformLayout();
            digital.ResumeLayout(false);
            digital_main_panel.ResumeLayout(false);
            pinA_group.ResumeLayout(false);
            pinA_panel.ResumeLayout(false);
            pinA_panel.PerformLayout();
            portC_group.ResumeLayout(false);
            portC_panel.ResumeLayout(false);
            portC_panel.PerformLayout();
            portCCheckbox_panel.ResumeLayout(false);
            portCCheckbox_panel.PerformLayout();
            lights.ResumeLayout(false);
            lights_main_panel.ResumeLayout(false);
            lights_ports_group.ResumeLayout(false);
            potPorts_panel.ResumeLayout(false);
            potPorts_panel.PerformLayout();
            light_lights_group.ResumeLayout(false);
            tableLayoutPanel2.ResumeLayout(false);
            lightPot_panel.ResumeLayout(false);
            lightPot_panel.PerformLayout();
            temp.ResumeLayout(false);
            temp_main_panel.ResumeLayout(false);
            temp_main_panel.PerformLayout();
            control_group.ResumeLayout(false);
            control_panel.ResumeLayout(false);
            control_panel.PerformLayout();
            controlSettings_panel.ResumeLayout(false);
            controlSettings_panel.PerformLayout();
            setPointTemp_panel.ResumeLayout(false);
            setPointTemp_panel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)setPointTemp_upDown).EndInit();
            pITuning_panel.ResumeLayout(false);
            pITuning_panel.PerformLayout();
            pITuningKValues_panel.ResumeLayout(false);
            pITuningKValues_panel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)pITuningKP_upDown).EndInit();
            ((System.ComponentModel.ISupportInitialize)pITuningKI_upDown).EndInit();
            tempMiscValues_panel.ResumeLayout(false);
            tempMiscValues_panel.PerformLayout();
            cloudDataLogging_group.ResumeLayout(false);
            cloudDataLogging_group.PerformLayout();
            cloudDataLogging_panel.ResumeLayout(false);
            cloudDataLogging_panel.PerformLayout();
            manualDataLogging_group.ResumeLayout(false);
            manualDataLogging_group.PerformLayout();
            manualDataLogging_panel.ResumeLayout(false);
            manualDataLogging_panel.PerformLayout();
            autoDataLogging_group.ResumeLayout(false);
            autoDataLogging_group.PerformLayout();
            autoDataLogging_panel.ResumeLayout(false);
            autoDataLogging_panel.PerformLayout();
            ResumeLayout(false);
        }

        private void label1_Click(object sender, EventArgs e)
        {
            throw new NotImplementedException();
        }

        #endregion

        private TabControl tabs;
        private TabPage setup;
        private TabPage digital;
        private TabPage lights;
        private TabPage temp;
        private GroupBox databaseServerConnection_group;
        private GroupBox serialPortConnection_group;
        private TableLayoutPanel dbServerConn_panel;
        private Label serverName_label;
        private TableLayoutPanel dbServerConnSettings_panel;
        private TextBox db_text;
        private TextBox password_text;
        private ComboBox serverName_drop;
        private TextBox username_text;
        private Label username_label;
        private Label password_label;
        private Label db_label;
        private TableLayoutPanel dbServerConnectionButtons_panel;
        private Button dbServerConnection_connect_button;
        private Button dbServerConnection_disconnect_button;
        private TableLayoutPanel databaseServerStatus_panel;
        private Label dbServerStatus_label;
        private TableLayoutPanel setup_main_panel;
        private TableLayoutPanel serialPortConnection_panel;
        private TableLayoutPanel serialPortConnectionSettings_panel;
        private Label comPort_label;
        private Label baudrate_label;
        private ComboBox comPort_text;
        private ComboBox baudrate_text;
        private TableLayoutPanel serialPortConnectionButtons_panel;
        private TableLayoutPanel serialPortStatus_panel;
        private Label serialPortStatus_label;
        private Button serialPortConnection_connect_button;
        private Button serialPortConnection_disconnect_button;
        private Bulb.LedBulb serialPortStatus_bulb;
        private Bulb.LedBulb dbServerConnStatus_bulb;
        private TableLayoutPanel digital_main_panel;
        private GroupBox pinA_group;
        private TableLayoutPanel pinA_panel;
        private GroupBox portC_group;
        private Bulb.LedBulb pa0_bulb;
        private Label pa0_label;
        private Label pa1_label;
        private Label pa2_label;
        private Label pa3_label;
        private Label pa4_label;
        private Label pa5_label;
        private Label pa6_label;
        private Label pa7_label;
        private Bulb.LedBulb pa1_bulb;
        private Bulb.LedBulb pa2_bulb;
        private Bulb.LedBulb pa3_bulb;
        private Bulb.LedBulb pa4_bulb;
        private Bulb.LedBulb pa5_bulb;
        private Bulb.LedBulb pa6_bulb;
        private Bulb.LedBulb pa7_bulb;
        private TableLayoutPanel portC_panel;
        private TableLayoutPanel portCCheckbox_panel;
        private CheckBox pc0;
        private CheckBox pc1;
        private CheckBox pc2;
        private CheckBox pc3;
        private CheckBox pc4;
        private CheckBox pc5;
        private CheckBox pc6;
        private CheckBox pc7;
        private DmitryBrant.CustomControls.SevenSegment sevenSegment1;
        private DmitryBrant.CustomControls.SevenSegment sevenSegment2;
        private TableLayoutPanel lights_main_panel;
        private GroupBox lights_ports_group;
        private GroupBox light_lights_group;
        private TableLayoutPanel potPorts_panel;
        private Label pot1_label;
        private Label pot2_label;
        private AquaControls.AquaGauge pot1;
        private AquaControls.AquaGauge pot2;
        private TableLayoutPanel tableLayoutPanel2;
        private AquaControls.AquaGauge lightPot;
        private TableLayoutPanel lightPot_panel;
        private VScrollBar lightPot_scroll;
        private TableLayoutPanel tableLayoutPanel4;
        private TextBox lightPot_text;
        private TableLayoutPanel temp_main_panel;
        private GroupBox cloudDataLogging_group;
        private TableLayoutPanel cloudDataLogging_panel;
        private GroupBox manualDataLogging_group;
        private GroupBox autoDataLogging_group;
        private TableLayoutPanel manualDataLogging_panel;
        private TextBox manualDataLogging_text;
        private Button manualDataLoggingInsert_button;
        private TableLayoutPanel autoDataLogging_panel;
        private Button autoDataLoggingEnable_button;
        private Button autoDataLoggingDisable_button;
        private Label autoDataLoggingStatus_label;
        private GroupBox control_group;
        private TableLayoutPanel control_panel;
        private TableLayoutPanel controlSettings_panel;
        private TableLayoutPanel setPointTemp_panel;
        private Label setPointTemp_label;
        private NumericUpDown setPointTemp_upDown;
        private TableLayoutPanel pITuning_panel;
        private Label pITuning_label;
        private TableLayoutPanel pITuningKValues_panel;
        private Label pITuningKP_label;
        private Label pITuningKI_label;
        private NumericUpDown pITuningKP_upDown;
        private NumericUpDown pITuningKI_upDown;
        private TableLayoutPanel tempMiscValues_panel;
        private Label actualTemp_label;
        private Label motorSpeed_label;
        private TextBox actualTemp_text;
        private TextBox motorSpeed_text;
    }
}
