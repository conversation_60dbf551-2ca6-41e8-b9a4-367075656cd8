#include <avr/io.h>

void timer_setup() {
  TCCR1A =
    (1 << COM1A1) |  // Clear on compare match for OC1A
    (1 << COM1B1) |  // Clear on compare match for OC1B
    (1 << COM1C1) |  // Clear on compare match for OC1C
    (1 << WGM11);    // Fast PWM mode (Mode 14 on table)

  TCCR1B =
    (1 << WGM13) |   // Fast PWM mode
    (1 << WGM12) |   // Fast PWM mode
    (1 << CS10);     // No prescaler

  // Initialize all compare values to 0
  OCR1A = 0;  // Motor
  OCR1B = 0;  // Light
  OCR1C = 0;  // Heater
}