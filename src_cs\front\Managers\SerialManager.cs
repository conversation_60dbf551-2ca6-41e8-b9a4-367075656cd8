﻿using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Threading;
using System.Windows.Forms;

namespace front.Managers
{
    public sealed class SerialManager
    {
        private static readonly Lazy<SerialManager> _instance = new(() => new SerialManager());
        public static SerialManager Instance => _instance.Value;
        public string PortName => _serialPort?.PortName;

        private SerialPort _serialPort;
        private readonly object _lock = new();
        private readonly AutoResetEvent _responseReady = new(false);
        private byte _lastResponse;
        private bool _awaitingResponse;

        private const byte START_BYTE = 0x53;
        private const byte STOP_BYTE = 0xAA;

        private SerialManager() { }

        public void Connect(string port, int baudRate)
        {
            try
            {
                if (_serialPort != null && _serialPort.IsOpen)
                    _serialPort.Close();

                _serialPort = new SerialPort(port, baudRate);
                _serialPort.DataReceived += SerialPort_DataReceived;
                _serialPort.Open();
            }
            catch (Exception ex)
            {
                ShowError("Serial Connection Failed", ex.Message);
            }
        }

        public void Disconnect()
        {
            try
            {
                _serialPort?.Close();
            }
            catch (Exception ex)
            {
                ShowError("Serial Disconnect Failed", ex.Message);
            }
        }

        public bool IsConnected => _serialPort != null && _serialPort.IsOpen;

        public void SendInstruction(byte instruction, ushort? data = null)
        {
            try
            {
                List<byte> packet = new() { START_BYTE, instruction };

                if (data.HasValue)
                {
                    packet.Add((byte)(data.Value & 0xFF));       // LSB
                    packet.Add((byte)((data.Value >> 8) & 0xFF)); // MSB
                }

                packet.Add(STOP_BYTE);
                _awaitingResponse = true;
                _serialPort.Write(packet.ToArray(), 0, packet.Count);
            }
            catch (Exception ex)
            {
                ShowError("Serial Write Failed", ex.Message);
            }
        }

        public byte SendAndRead(byte instruction)
        {
            SendInstruction(instruction);
            if (_responseReady.WaitOne(200))
            {
                return _lastResponse;
            }
            else
            {
                throw new TimeoutException("No response from MCU.");
            }
        }

        private void SerialPort_DataReceived(object sender, SerialDataReceivedEventArgs e)
        {
            try
            {
                if (_awaitingResponse && _serialPort.BytesToRead > 0)
                {
                    _lastResponse = (byte)_serialPort.ReadByte();
                    _awaitingResponse = false;
                    _responseReady.Set();
                }
            }
            catch (Exception ex)
            {
                ShowError("Serial Read Failed", ex.Message);
            }
        }

        public string GetConnectedPortName()
        {
            return _serialPort?.IsOpen == true ? _serialPort.PortName : "N/A";
        }


        private void ShowError(string title, string msg)
        {
            MessageBox.Show(msg, title, MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        public static string[] GetSerialPorts() => SerialPort.GetPortNames();

        public static int[] GetBaudRates() => new int[] { 9600, 19200, 38400, 57600, 115200 };

        //----- Instruction-specific functions -----

        public byte CheckTx() => SendAndRead(0x00);

        public byte ReadPINA() => SendAndRead(0x01);

        public byte ReadPot1() => SendAndRead(0x02);

        public byte ReadPot2() => SendAndRead(0x03);

        public byte ReadTemp() => SendAndRead(0x04);

        public byte ReadLight() => SendAndRead(0x05);

        public void SetPortC(ushort value) => SendInstruction(0x0A, value);

        public void SetHeater(ushort value) => SendInstruction(0x0B, value);

        public void SetLight(ushort value) => SendInstruction(0x0C, value);

        public void SetMotor(ushort value) => SendInstruction(0x0D, value);
    }
}
