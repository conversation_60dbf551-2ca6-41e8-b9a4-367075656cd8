#include "setup.h"
#include <avr/io.h>
#include <avr/interrupt.h>
#include <stdbool.h>

#include "globals.h"

bool read_and_transmit_adc_value(uint8_t channel) {
  if (!is_adc_running)
    get_ADC_val(channel);

  if (is_adc_complete) {
    is_adc_complete = false;
    transmit_byte(adc_ms_byte);
    return true;  // ADC and TX complete
  }
  return false;  // Incomplete
}


int main(){
  setup();
  while(true) {
    switch (state) {
      case AWAIT:
        break;

      case RECEIVING:
        break;

      case PROCESS_SHORT_INSTRUCTION:
        switch (instruction_byte) {
          case TXCHECK: // Retrun 0x0F to inidcate the communications is working
            transmit_byte(0x0F);
            state = AWAIT;
            break;
          case READ_PINA: // Return the contents of PINA
            transmit_byte(PINA);
            state = AWAIT;
            break;
          case READ_POT1: // Read ADC Channel 2 and return ADCH
            if (read_and_transmit_adc_value(2)) state = AWAIT;
            break;
          case READ_POT2: // Read ADC Channel 1 and return ADCH
            if (read_and_transmit_adc_value(1)) state = AWAIT;
            break;
          case READ_TEMP: // Read ADC Channel 3 and return ADCH
            if (read_and_transmit_adc_value(3)) state = AWAIT;
            break;
          case READ_LIGHT: // Read ADC Channel 0 and return ADCH
            if (read_and_transmit_adc_value(0)) state = AWAIT;
            break;
        }
        break;

      case PROCESS_LONG_INSTRUCTION:
        switch(instruction_byte) {
          case SET_PORTC: // Write the 8 LSB of the received integer to PORTC, return 0x0A
            PORTC = instruction_data_ls_byte;
            transmit_byte(0x0A);
            break;
          case SET_HEATER: // Write the received integer to OCR1C, return 0x0B
            OCR1CL = instruction_data_ls_byte;
            OCR1CH = instruction_data_ms_byte;
            transmit_byte(0x0B);
            break;
          case SET_LIGHT: // Write the received integer to OCR1B, return 0x0C
            OCR1BL = instruction_data_ls_byte;
            OCR1BH = instruction_data_ms_byte;
            transmit_byte(0x0C);
            break;
          case SET_MOTOR: // Write the received integer to OCR1A, return 0x0D
            OCR1AL = instruction_data_ls_byte;
            OCR1AH = instruction_data_ms_byte;
            transmit_byte(0x0D);
            break;
        }
        break;
    }
  };
}



