C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\obj\Debug\net8.0-windows\front.csproj.GenerateResource.cache
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\obj\Debug\net8.0-windows\front.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\obj\Debug\net8.0-windows\front.AssemblyInfoInputs.cache
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\obj\Debug\net8.0-windows\front.AssemblyInfo.cs
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\obj\Debug\net8.0-windows\front.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\front.exe
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\front.deps.json
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\front.runtimeconfig.json
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\front.dll
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\front.pdb
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\obj\Debug\net8.0-windows\front.dll
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\obj\Debug\net8.0-windows\refint\front.dll
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\obj\Debug\net8.0-windows\front.pdb
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\obj\Debug\net8.0-windows\front.genruntimeconfig.cache
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\obj\Debug\net8.0-windows\ref\front.dll
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\obj\Debug\net8.0-windows\AquaControls.AquaGauge.resources
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\LiveCharts.dll
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\LiveCharts.WinForms.dll
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\LiveCharts.Wpf.dll
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\obj\Debug\net8.0-windows\front.csproj.AssemblyReference.cache
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\obj\Debug\net8.0-windows\front.csproj.Up2Date
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\obj\Debug\net8.0-windows\front.MainForm.resources
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\System.IO.Ports.dll
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\runtimes\android-arm\native\libSystem.IO.Ports.Native.so
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\runtimes\android-arm64\native\libSystem.IO.Ports.Native.so
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\runtimes\android-x64\native\libSystem.IO.Ports.Native.so
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\runtimes\android-x86\native\libSystem.IO.Ports.Native.so
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\runtimes\linux-arm\native\libSystem.IO.Ports.Native.so
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\runtimes\linux-arm64\native\libSystem.IO.Ports.Native.so
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\runtimes\linux-bionic-arm64\native\libSystem.IO.Ports.Native.so
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\runtimes\linux-bionic-x64\native\libSystem.IO.Ports.Native.so
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\runtimes\linux-musl-arm\native\libSystem.IO.Ports.Native.so
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\runtimes\linux-musl-arm64\native\libSystem.IO.Ports.Native.so
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\runtimes\linux-musl-x64\native\libSystem.IO.Ports.Native.so
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\runtimes\linux-x64\native\libSystem.IO.Ports.Native.so
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\runtimes\maccatalyst-arm64\native\libSystem.IO.Ports.Native.dylib
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\runtimes\maccatalyst-x64\native\libSystem.IO.Ports.Native.dylib
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\runtimes\osx-arm64\native\libSystem.IO.Ports.Native.dylib
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\runtimes\osx-x64\native\libSystem.IO.Ports.Native.dylib
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\runtimes\unix\lib\net8.0\System.IO.Ports.dll
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\runtimes\win\lib\net8.0\System.IO.Ports.dll
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\BouncyCastle.Cryptography.dll
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\Google.Protobuf.dll
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\K4os.Compression.LZ4.dll
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\K4os.Compression.LZ4.Streams.dll
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\K4os.Hash.xxHash.dll
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\MySql.Data.dll
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\System.IO.Pipelines.dll
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\System.Text.Encodings.Web.dll
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\System.Text.Json.dll
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\ZstdSharp.dll
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\runtimes\win-x64\native\comerr64.dll
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\runtimes\win-x64\native\gssapi64.dll
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\runtimes\win-x64\native\k5sprt64.dll
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\runtimes\win-x64\native\krb5_64.dll
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\runtimes\win-x64\native\krbcc64.dll
C:\Users\<USER>\Documents\Uni\2025s1\embedded systems design\project\src_cs\front\bin\Debug\net8.0-windows\runtimes\browser\lib\net8.0\System.Text.Encodings.Web.dll
